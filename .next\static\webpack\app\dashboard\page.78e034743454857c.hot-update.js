"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n// Serviço para integração com Firebase Functions de IA\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            var _requestData_message, _requestData_message1, _requestData_message2;\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model\n            };\n            // DEBUG: Log detalhado no console do navegador\n            console.group(\"\\uD83E\\uDD16 AI SERVICE - ENVIANDO REQUISI\\xc7\\xc3O\");\n            console.log(\"\\uD83D\\uDCE1 URL da Function:\", this.functionUrl);\n            console.log(\"\\uD83D\\uDCCB Dados da Requisi\\xe7\\xe3o:\", {\n                username: requestData.username,\n                chatId: requestData.chatId,\n                message: requestData.message,\n                messageLength: ((_requestData_message = requestData.message) === null || _requestData_message === void 0 ? void 0 : _requestData_message.length) || 0,\n                messagePreview: ((_requestData_message1 = requestData.message) === null || _requestData_message1 === void 0 ? void 0 : _requestData_message1.substring(0, 100)) + (((_requestData_message2 = requestData.message) === null || _requestData_message2 === void 0 ? void 0 : _requestData_message2.length) > 100 ? \"...\" : \"\"),\n                model: requestData.model,\n                timestamp: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDD27 Headers:\", {\n                \"Content-Type\": \"application/json\",\n                \"Method\": \"POST\"\n            });\n            console.log(\"\\uD83D\\uDCE6 Body JSON:\", JSON.stringify(requestData, null, 2));\n            console.log(\"\\uD83D\\uDCCA Estat\\xedsticas:\", {\n                bodySize: JSON.stringify(requestData).length + \" bytes\",\n                hasAbortController: !!this.abortController,\n                userAgent: navigator.userAgent\n            });\n            console.groupEnd();\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            // DEBUG: Log da resposta\n            console.group(\"\\uD83D\\uDCE1 AI SERVICE - RESPOSTA RECEBIDA\");\n            console.log(\"✅ Status:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCCB Headers:\", Object.fromEntries(response.headers.entries()));\n            console.log(\"\\uD83D\\uDD0D Response OK:\", response.ok);\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            console.group(\"\\uD83D\\uDD04 AI SERVICE - PROCESSANDO STREAM\");\n            console.log(\"\\uD83D\\uDCD6 Reader criado:\", !!reader);\n            console.log(\"⏰ In\\xedcio do stream:\", new Date().toISOString());\n            let chunkCount = 0;\n            let totalBytes = 0;\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        console.log(\"✅ Stream finalizado\");\n                        console.log(\"\\uD83D\\uDCCA Estat\\xedsticas finais:\", {\n                            totalChunks: chunkCount,\n                            totalBytes: totalBytes,\n                            responseLength: fullResponse.length,\n                            avgChunkSize: chunkCount > 0 ? Math.round(totalBytes / chunkCount) : 0\n                        });\n                        console.groupEnd();\n                        break;\n                    }\n                    chunkCount++;\n                    totalBytes += (value === null || value === void 0 ? void 0 : value.length) || 0;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    if (chunkCount <= 5) {\n                        console.log(\"\\uD83D\\uDCE6 Chunk \".concat(chunkCount, \":\"), {\n                            size: chunk.length,\n                            content: chunk.substring(0, 100) + (chunk.length > 100 ? \"...\" : \"\"),\n                            timestamp: new Date().toISOString()\n                        });\n                    }\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                console.group(\"✅ AI SERVICE - RESPOSTA COMPLETA\");\n                console.log(\"\\uD83D\\uDCDD Resposta final:\", {\n                    length: fullResponse.length,\n                    preview: fullResponse.substring(0, 200) + (fullResponse.length > 200 ? \"...\" : \"\"),\n                    wordCount: fullResponse.split(\" \").length,\n                    timestamp: new Date().toISOString()\n                });\n                console.groupEnd();\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    console.log(\"Requisi\\xe7\\xe3o cancelada pelo usu\\xe1rio\");\n                    return;\n                }\n                onError(error.message);\n            } else {\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        try {\n            this.validateConfig(config);\n            await this.sendMessage(config, onChunk, onComplete, onError);\n        } catch (error) {\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});