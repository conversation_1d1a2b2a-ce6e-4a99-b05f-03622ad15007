"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n// Serviço para integração com Firebase Functions de IA\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            var _requestData_message, _requestData_message1, _requestData_message2;\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model\n            };\n            // DEBUG: Log detalhado no console do navegador\n            console.group(\"\\uD83E\\uDD16 AI SERVICE - ENVIANDO REQUISI\\xc7\\xc3O\");\n            console.log(\"\\uD83D\\uDCE1 URL da Function:\", this.functionUrl);\n            console.log(\"\\uD83D\\uDCCB Dados da Requisi\\xe7\\xe3o:\", {\n                username: requestData.username,\n                chatId: requestData.chatId,\n                message: requestData.message,\n                messageLength: ((_requestData_message = requestData.message) === null || _requestData_message === void 0 ? void 0 : _requestData_message.length) || 0,\n                messagePreview: ((_requestData_message1 = requestData.message) === null || _requestData_message1 === void 0 ? void 0 : _requestData_message1.substring(0, 100)) + (((_requestData_message2 = requestData.message) === null || _requestData_message2 === void 0 ? void 0 : _requestData_message2.length) > 100 ? \"...\" : \"\"),\n                model: requestData.model,\n                timestamp: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDD27 Headers:\", {\n                \"Content-Type\": \"application/json\",\n                \"Method\": \"POST\"\n            });\n            console.log(\"\\uD83D\\uDCE6 Body JSON:\", JSON.stringify(requestData, null, 2));\n            console.log(\"\\uD83D\\uDCCA Estat\\xedsticas:\", {\n                bodySize: JSON.stringify(requestData).length + \" bytes\",\n                hasAbortController: !!this.abortController,\n                userAgent: navigator.userAgent\n            });\n            // Buscar configurações do chat do Firestore para mostrar nos logs\n            console.log(\"\\uD83D\\uDD0D Buscando configura\\xe7\\xf5es do chat do Firestore para debug...\");\n            try {\n                // Importar Firebase dinamicamente para evitar problemas de SSR\n                const { doc, getDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                const { db } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\"));\n                const chatDocRef = doc(db, \"usuarios\", requestData.username, \"conversas\", requestData.chatId);\n                const chatDocSnap = await getDoc(chatDocRef);\n                if (chatDocSnap.exists()) {\n                    var _chatConfig_systemPrompt, _chatConfig_systemPrompt1, _chatConfig_systemPrompt2, _chatConfig_context, _chatConfig_context1, _chatConfig_context2;\n                    const chatConfig = chatDocSnap.data();\n                    console.group(\"⚙️ CONFIGURA\\xc7\\xd5ES DO CHAT (FIRESTORE)\");\n                    console.log(\"\\uD83C\\uDFAF System Prompt:\", {\n                        length: ((_chatConfig_systemPrompt = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt === void 0 ? void 0 : _chatConfig_systemPrompt.length) || 0,\n                        content: chatConfig.systemPrompt || \"(vazio)\",\n                        preview: ((_chatConfig_systemPrompt1 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt1 === void 0 ? void 0 : _chatConfig_systemPrompt1.substring(0, 200)) + (((_chatConfig_systemPrompt2 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt2 === void 0 ? void 0 : _chatConfig_systemPrompt2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83D\\uDCDD Contexto:\", {\n                        length: ((_chatConfig_context = chatConfig.context) === null || _chatConfig_context === void 0 ? void 0 : _chatConfig_context.length) || 0,\n                        content: chatConfig.context || \"(vazio)\",\n                        preview: ((_chatConfig_context1 = chatConfig.context) === null || _chatConfig_context1 === void 0 ? void 0 : _chatConfig_context1.substring(0, 200)) + (((_chatConfig_context2 = chatConfig.context) === null || _chatConfig_context2 === void 0 ? void 0 : _chatConfig_context2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83C\\uDF21️ Par\\xe2metros de Gera\\xe7\\xe3o:\", {\n                        temperature: chatConfig.temperature,\n                        temperatureType: typeof chatConfig.temperature,\n                        frequencyPenalty: chatConfig.frequencyPenalty,\n                        frequencyPenaltyType: typeof chatConfig.frequencyPenalty,\n                        repetitionPenalty: chatConfig.repetitionPenalty,\n                        repetitionPenaltyType: typeof chatConfig.repetitionPenalty,\n                        maxTokens: chatConfig.maxTokens,\n                        maxTokensType: typeof chatConfig.maxTokens,\n                        latexInstructions: chatConfig.latexInstructions\n                    });\n                    console.log(\"\\uD83D\\uDCCA Metadados:\", {\n                        lastUsedModel: chatConfig.lastUsedModel,\n                        createdAt: chatConfig.createdAt,\n                        lastUpdatedAt: chatConfig.lastUpdatedAt,\n                        name: chatConfig.name\n                    });\n                    console.groupEnd();\n                } else {\n                    console.warn(\"⚠️ Documento do chat n\\xe3o encontrado no Firestore\");\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar configura\\xe7\\xf5es do chat do Firestore:\", error);\n            }\n            // Buscar histórico de mensagens para mostrar nos logs\n            try {\n                const messagesResponse = await fetch(\"/api/chat/\".concat(requestData.username, \"/\").concat(requestData.chatId));\n                if (messagesResponse.ok) {\n                    const chatData = await messagesResponse.json();\n                    const messages = chatData.messages || [];\n                    console.group(\"\\uD83D\\uDCAC HIST\\xd3RICO DE MENSAGENS\");\n                    console.log(\"\\uD83D\\uDCCA Total de mensagens:\", messages.length);\n                    console.log(\"\\uD83D\\uDCCB \\xdaltimas 5 mensagens:\", messages.slice(-5).map((msg, index)=>{\n                        var _msg_content, _msg_content1, _msg_content2;\n                        return {\n                            index: messages.length - 5 + index,\n                            id: msg.id,\n                            role: msg.role,\n                            contentLength: ((_msg_content = msg.content) === null || _msg_content === void 0 ? void 0 : _msg_content.length) || 0,\n                            contentPreview: ((_msg_content1 = msg.content) === null || _msg_content1 === void 0 ? void 0 : _msg_content1.substring(0, 100)) + (((_msg_content2 = msg.content) === null || _msg_content2 === void 0 ? void 0 : _msg_content2.length) > 100 ? \"...\" : \"\"),\n                            timestamp: msg.timestamp\n                        };\n                    }));\n                    if (messages.length > 5) {\n                        console.log(\"\\uD83D\\uDCDC Mensagens mais antigas:\", \"\".concat(messages.length - 5, \" mensagens anteriores n\\xe3o mostradas\"));\n                    }\n                    console.groupEnd();\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar hist\\xf3rico de mensagens:\", error);\n            }\n            console.groupEnd();\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            // DEBUG: Log da resposta\n            console.group(\"\\uD83D\\uDCE1 AI SERVICE - RESPOSTA RECEBIDA\");\n            console.log(\"✅ Status:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCCB Headers:\", Object.fromEntries(response.headers.entries()));\n            console.log(\"\\uD83D\\uDD0D Response OK:\", response.ok);\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            console.group(\"\\uD83D\\uDD04 AI SERVICE - PROCESSANDO STREAM\");\n            console.log(\"\\uD83D\\uDCD6 Reader criado:\", !!reader);\n            console.log(\"⏰ In\\xedcio do stream:\", new Date().toISOString());\n            let chunkCount = 0;\n            let totalBytes = 0;\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        console.log(\"✅ Stream finalizado\");\n                        console.log(\"\\uD83D\\uDCCA Estat\\xedsticas finais:\", {\n                            totalChunks: chunkCount,\n                            totalBytes: totalBytes,\n                            responseLength: fullResponse.length,\n                            avgChunkSize: chunkCount > 0 ? Math.round(totalBytes / chunkCount) : 0\n                        });\n                        console.groupEnd();\n                        break;\n                    }\n                    chunkCount++;\n                    totalBytes += (value === null || value === void 0 ? void 0 : value.length) || 0;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    if (chunkCount <= 5) {\n                        console.log(\"\\uD83D\\uDCE6 Chunk \".concat(chunkCount, \":\"), {\n                            size: chunk.length,\n                            content: chunk.substring(0, 100) + (chunk.length > 100 ? \"...\" : \"\"),\n                            timestamp: new Date().toISOString()\n                        });\n                    }\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                console.group(\"✅ AI SERVICE - RESPOSTA COMPLETA\");\n                console.log(\"\\uD83D\\uDCDD Resposta final:\", {\n                    length: fullResponse.length,\n                    preview: fullResponse.substring(0, 200) + (fullResponse.length > 200 ? \"...\" : \"\"),\n                    wordCount: fullResponse.split(\" \").length,\n                    timestamp: new Date().toISOString()\n                });\n                console.groupEnd();\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            console.group(\"❌ AI SERVICE - ERRO CAPTURADO\");\n            console.error(\"\\uD83D\\uDEA8 Tipo do erro:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.error(\"\\uD83D\\uDCDD Mensagem:\", error instanceof Error ? error.message : String(error));\n            console.error(\"\\uD83D\\uDD0D Stack trace:\", error instanceof Error ? error.stack : \"N/A\");\n            console.error(\"⏰ Timestamp:\", new Date().toISOString());\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    console.log(\"\\uD83D\\uDED1 Requisi\\xe7\\xe3o cancelada pelo usu\\xe1rio\");\n                    console.groupEnd();\n                    return;\n                }\n                console.error(\"\\uD83D\\uDD27 Detalhes completos:\", error);\n                console.groupEnd();\n                onError(error.message);\n            } else {\n                console.error(\"\\uD83D\\uDD27 Detalhes completos:\", error);\n                console.groupEnd();\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        var _config_message;\n        console.group(\"\\uD83D\\uDE80 AI SERVICE - SEND MESSAGE SAFE\");\n        console.log(\"\\uD83D\\uDD27 Config recebida:\", {\n            username: config.username,\n            chatId: config.chatId,\n            messageLength: ((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.length) || 0,\n            model: config.model,\n            timestamp: new Date().toISOString()\n        });\n        console.log(\"\\uD83D\\uDCCB Callbacks:\", {\n            hasOnChunk: typeof onChunk === \"function\",\n            hasOnComplete: typeof onComplete === \"function\",\n            hasOnError: typeof onError === \"function\"\n        });\n        try {\n            console.log(\"✅ Validando configura\\xe7\\xe3o...\");\n            this.validateConfig(config);\n            console.log(\"✅ Configura\\xe7\\xe3o v\\xe1lida, enviando mensagem...\");\n            await this.sendMessage(config, onChunk, onComplete, onError);\n            console.log(\"✅ Mensagem enviada com sucesso\");\n            console.groupEnd();\n        } catch (error) {\n            console.group(\"❌ ERRO EM SEND MESSAGE SAFE\");\n            console.error(\"\\uD83D\\uDEA8 Erro capturado:\", error);\n            console.error(\"\\uD83D\\uDCDD Tipo:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.groupEnd();\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n            console.groupEnd();\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});