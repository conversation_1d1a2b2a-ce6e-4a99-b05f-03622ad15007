'use client';

import { useRef, useEffect, useState } from 'react';
import <PERSON>down<PERSON>ender<PERSON> from './MarkdownRenderer';
import DeleteMessageModal from './DeleteMessageModal';
import RegenerateMessageModal from './RegenerateMessageModal';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: string;
  isFavorite?: boolean;
}

interface ChatInterfaceProps {
  messages: Message[];
  isLoading: boolean;
  isLoadingChat: boolean;
  onDeleteMessage: (messageId: string) => Promise<void>;
  onRegenerateMessage: (messageId: string) => Promise<void>;
  onEditMessage: (messageId: string, newContent: string) => void;
  onCopyMessage: (content: string) => void;
}

export default function ChatInterface({
  messages,
  isLoading,
  isLoadingChat,
  onDeleteMessage,
  onRegenerateMessage,
  onEditMessage,
  onCopyMessage
}: ChatInterfaceProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editingContent, setEditingContent] = useState<string>('');
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    messageId: string;
    messageContent: string;
    isDeleting: boolean;
  }>({
    isOpen: false,
    messageId: '',
    messageContent: '',
    isDeleting: false
  });

  const [regenerateModal, setRegenerateModal] = useState<{
    isOpen: boolean;
    messageId: string;
    messageContent: string;
    messagesAffectedCount: number;
    isRegenerating: boolean;
  }>({
    isOpen: false,
    messageId: '',
    messageContent: '',
    messagesAffectedCount: 0,
    isRegenerating: false
  });

  const scrollToBottom = () => {
    // Usar requestAnimationFrame para otimizar o scroll
    requestAnimationFrame(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    });
  };

  useEffect(() => {
    // Debounce o scroll para evitar múltiplas chamadas
    const timeoutId = setTimeout(() => {
      scrollToBottom();
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [messages]);

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleStartEdit = (message: Message) => {
    setEditingMessageId(message.id);
    setEditingContent(message.content);
  };

  const handleSaveEdit = () => {
    if (editingMessageId && editingContent.trim() !== '') {
      onEditMessage(editingMessageId, editingContent.trim());
    }
    setEditingMessageId(null);
    setEditingContent('');
  };

  const handleCancelEdit = () => {
    setEditingMessageId(null);
    setEditingContent('');
  };

  const handleDeleteClick = (message: Message) => {
    setDeleteModal({
      isOpen: true,
      messageId: message.id,
      messageContent: message.content,
      isDeleting: false
    });
  };

  const handleDeleteConfirm = async () => {
    setDeleteModal(prev => ({ ...prev, isDeleting: true }));

    try {
      await onDeleteMessage(deleteModal.messageId);
      setDeleteModal({
        isOpen: false,
        messageId: '',
        messageContent: '',
        isDeleting: false
      });
    } catch (error) {
      console.error('Erro ao deletar mensagem:', error);
      setDeleteModal(prev => ({ ...prev, isDeleting: false }));
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModal({
      isOpen: false,
      messageId: '',
      messageContent: '',
      isDeleting: false
    });
  };

  const handleRegenerateClick = (message: Message) => {
    // Encontrar o índice da mensagem atual
    const messageIndex = messages.findIndex(msg => msg.id === message.id);

    // Contar quantas mensagens vêm depois desta
    const messagesAfterCount = messages.length - messageIndex - 1;

    setRegenerateModal({
      isOpen: true,
      messageId: message.id,
      messageContent: message.content,
      messagesAffectedCount: messagesAfterCount,
      isRegenerating: false
    });
  };

  const handleRegenerateConfirm = async () => {
    setRegenerateModal(prev => ({ ...prev, isRegenerating: true }));

    try {
      await onRegenerateMessage(regenerateModal.messageId);
      setRegenerateModal({
        isOpen: false,
        messageId: '',
        messageContent: '',
        messagesAffectedCount: 0,
        isRegenerating: false
      });
    } catch (error) {
      console.error('Erro ao regenerar mensagem:', error);
      setRegenerateModal(prev => ({ ...prev, isRegenerating: false }));
    }
  };

  const handleRegenerateCancel = () => {
    setRegenerateModal({
      isOpen: false,
      messageId: '',
      messageContent: '',
      messagesAffectedCount: 0,
      isRegenerating: false
    });
  };

  const MessageActions = ({ message, isUser }: { message: Message; isUser: boolean }) => (
    <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
      {/* Botão Excluir */}
      <button
        onClick={() => handleDeleteClick(message)}
        className="p-1.5 text-white/40 hover:text-red-400 hover:bg-red-400/10 rounded transition-all duration-200 hover:scale-110"
        title="Excluir mensagem"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      </button>

      {/* Botão Regenerar (apenas para usuário) */}
      {isUser && (
        <button
          onClick={() => handleRegenerateClick(message)}
          className="p-1.5 text-white/40 hover:text-blue-400 hover:bg-blue-400/10 rounded transition-all duration-200 hover:scale-110"
          title="Regenerar mensagem"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      )}



      {/* Botão Editar */}
      <button
        onClick={() => handleStartEdit(message)}
        className="p-1.5 text-white/40 hover:text-green-400 hover:bg-green-400/10 rounded transition-all duration-200 hover:scale-110"
        title="Editar mensagem"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
      </button>

      {/* Botão Copiar */}
      <button
        onClick={() => onCopyMessage(message.content)}
        className="p-1.5 text-white/40 hover:text-purple-400 hover:bg-purple-400/10 rounded transition-all duration-200 hover:scale-110"
        title="Copiar mensagem"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
            d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      </button>
    </div>
  );

  return (
    <div className="h-full overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent" style={{ maxHeight: '100%' }}>
      {isLoadingChat ? (
        // Estado de carregamento de chat
        <div className="h-full flex items-center justify-center">
          <div className="text-center max-w-md mx-auto">
            {/* Ícone de carregamento */}
            <div className="mb-8">
              <div className="w-20 h-20 mx-auto bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-blue-500/30 backdrop-blur-sm">
                <svg className="w-10 h-10 text-blue-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </div>
            </div>

            {/* Texto de carregamento */}
            <div className="space-y-4">
              <h2 className="text-2xl font-semibold text-white/90 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                Carregando mensagens
              </h2>
              <p className="text-white/60 text-base leading-relaxed">
                Aguarde enquanto carregamos o histórico da conversa...
              </p>

              {/* Indicador de progresso */}
              <div className="mt-6">
                <div className="w-48 h-1 bg-gray-700/50 rounded-full mx-auto overflow-hidden">
                  <div className="h-full bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : messages.length === 0 ? (
        // Área vazia quando não há mensagens - centralizada e mais bonita
        <div className="h-full flex items-center justify-center">
          <div className="text-center max-w-md mx-auto">
            {/* Ícone decorativo */}
            <div className="mb-8">
              <div className="w-20 h-20 mx-auto bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-blue-500/30 backdrop-blur-sm">
                <svg className="w-10 h-10 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
            </div>

            {/* Texto principal */}
            <div className="space-y-4">
              <h2 className="text-2xl font-semibold text-white/90 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                Comece uma nova conversa
              </h2>
              <p className="text-white/60 text-base leading-relaxed">
                Digite sua mensagem abaixo para começar a conversar com a IA
              </p>

              {/* Sugestões de uso */}
              <div className="mt-8 grid grid-cols-1 gap-3">
                <div className="bg-blue-900/20 backdrop-blur-sm rounded-lg p-4 border border-blue-600/20 hover:border-blue-500/40 transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    </div>
                    <div className="text-left">
                      <p className="text-white/80 text-sm font-medium">Faça perguntas</p>
                      <p className="text-white/50 text-xs">Tire dúvidas sobre qualquer assunto</p>
                    </div>
                  </div>
                </div>

                <div className="bg-cyan-900/20 backdrop-blur-sm rounded-lg p-4 border border-cyan-600/20 hover:border-cyan-500/40 transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-cyan-500/20 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                      </svg>
                    </div>
                    <div className="text-left">
                      <p className="text-white/80 text-sm font-medium">Peça ajuda com código</p>
                      <p className="text-white/50 text-xs">Programação, debugging e explicações</p>
                    </div>
                  </div>
                </div>

                <div className="bg-purple-900/20 backdrop-blur-sm rounded-lg p-4 border border-purple-600/20 hover:border-purple-500/40 transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </div>
                    <div className="text-left">
                      <p className="text-white/80 text-sm font-medium">Crie conteúdo</p>
                      <p className="text-white/50 text-xs">Textos, resumos e ideias criativas</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        // Container das mensagens com altura controlada
        <div className="space-y-4 min-h-0">{
        // Mensagens do chat
        messages.map((message) => (
          <div key={message.id} className={`flex items-start space-x-2 group animate-message-slide-in ${
            message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''
          }`}>
            {/* Avatar */}
            <div className={`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg border-2 ${
              message.sender === 'user'
                ? 'bg-gradient-to-br from-green-400 via-emerald-500 to-green-600 border-green-400/30'
                : 'bg-gradient-to-br from-blue-400 via-purple-500 to-blue-600 border-blue-400/30'
            }`}>
              <span className="text-white text-sm font-bold drop-shadow-sm">
                {message.sender === 'user' ? 'U' : 'AI'}
              </span>
            </div>

            {/* Mensagem */}
            <div className="flex-1">
              <div className={`backdrop-blur-sm rounded-2xl p-3 max-w-3xl border ${
                message.sender === 'user'
                  ? 'bg-green-600/20 border-green-500/20 rounded-tr-md ml-auto'
                  : 'bg-blue-600/20 border-blue-500/20 rounded-tl-md'
              }`}>
                {editingMessageId === message.id ? (
                  // Campo de edição inline
                  <div className="space-y-3">
                    <textarea
                      value={editingContent}
                      onChange={(e) => setEditingContent(e.target.value)}
                      className="w-full min-h-[120px] p-3 bg-gray-800/50 border border-gray-600/30 rounded-lg text-white placeholder-gray-400 resize-y focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50"
                      placeholder="Digite sua mensagem..."
                      autoFocus
                    />
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={handleCancelEdit}
                        className="px-3 py-1.5 text-sm text-gray-400 hover:text-white bg-gray-700/50 hover:bg-gray-600/50 rounded-md transition-all duration-200"
                      >
                        Cancelar
                      </button>
                      <button
                        onClick={handleSaveEdit}
                        className="px-3 py-1.5 text-sm text-white bg-blue-600 hover:bg-blue-500 rounded-md transition-all duration-200"
                      >
                        Salvar
                      </button>
                    </div>
                  </div>
                ) : (
                  // Conteúdo normal da mensagem com suporte a Markdown e LaTeX
                  <MarkdownRenderer content={message.content} />
                )}
              </div>
              
              {/* Timestamp e Ações próximos, dentro da mensagem */}
              <div className={`flex items-center gap-3 mt-2 ${
                message.sender === 'user' ? 'justify-end' : 'justify-start'
              }`}>
                {message.sender === 'user' ? (
                  <>
                    <MessageActions message={message} isUser={message.sender === 'user'} />
                    <p className="text-white/40 text-xs">
                      {formatTime(message.timestamp)}
                    </p>
                  </>
                ) : (
                  <>
                    <p className="text-white/40 text-xs">
                      {formatTime(message.timestamp)}
                    </p>
                    <MessageActions message={message} isUser={message.sender === 'user'} />
                  </>
                )}
              </div>
            </div>
          </div>
        ))
        }

        {/* Indicador de carregamento */}
        {isLoading && (
          <div className="flex items-start space-x-2 animate-message-slide-in">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-400 via-purple-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0 animate-pulse shadow-lg border-2 border-blue-400/30">
              <span className="text-white text-sm font-bold drop-shadow-sm">AI</span>
            </div>
            <div className="flex-1">
              <div className="bg-blue-600/20 backdrop-blur-sm rounded-2xl rounded-tl-md p-3 max-w-3xl border border-blue-500/20 shimmer-effect">
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-white/60 rounded-full animate-typing"></div>
                    <div className="w-2 h-2 bg-white/60 rounded-full animate-typing" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-2 h-2 bg-white/60 rounded-full animate-typing" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                  <span className="text-white/60 text-sm animate-pulse">Digitando...</span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} className="h-0" />
        </div>
      )}

      {/* Modal de confirmação de exclusão */}
      <DeleteMessageModal
        isOpen={deleteModal.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        messagePreview={deleteModal.messageContent}
        isDeleting={deleteModal.isDeleting}
      />

      {/* Modal de confirmação de regeneração */}
      <RegenerateMessageModal
        isOpen={regenerateModal.isOpen}
        onClose={handleRegenerateCancel}
        onConfirm={handleRegenerateConfirm}
        messagePreview={regenerateModal.messageContent}
        messagesAffectedCount={regenerateModal.messagesAffectedCount}
        isRegenerating={regenerateModal.isRegenerating}
      />
    </div>
  );
}
