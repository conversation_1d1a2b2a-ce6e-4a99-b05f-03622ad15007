"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n// Serviço para integração com Firebase Functions de IA\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            var _requestData_message, _requestData_message1, _requestData_message2;\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model\n            };\n            // DEBUG: Log detalhado no console do navegador\n            console.group(\"\\uD83E\\uDD16 AI SERVICE - ENVIANDO REQUISI\\xc7\\xc3O\");\n            console.log(\"\\uD83D\\uDCE1 URL da Function:\", this.functionUrl);\n            console.log(\"\\uD83D\\uDCCB Dados da Requisi\\xe7\\xe3o:\", {\n                username: requestData.username,\n                chatId: requestData.chatId,\n                message: requestData.message,\n                messageLength: ((_requestData_message = requestData.message) === null || _requestData_message === void 0 ? void 0 : _requestData_message.length) || 0,\n                messagePreview: ((_requestData_message1 = requestData.message) === null || _requestData_message1 === void 0 ? void 0 : _requestData_message1.substring(0, 100)) + (((_requestData_message2 = requestData.message) === null || _requestData_message2 === void 0 ? void 0 : _requestData_message2.length) > 100 ? \"...\" : \"\"),\n                model: requestData.model,\n                timestamp: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDD27 Headers:\", {\n                \"Content-Type\": \"application/json\",\n                \"Method\": \"POST\"\n            });\n            console.log(\"\\uD83D\\uDCE6 Body JSON:\", JSON.stringify(requestData, null, 2));\n            console.log(\"\\uD83D\\uDCCA Estat\\xedsticas:\", {\n                bodySize: JSON.stringify(requestData).length + \" bytes\",\n                hasAbortController: !!this.abortController,\n                userAgent: navigator.userAgent\n            });\n            // Buscar configurações do chat do Firestore para mostrar nos logs\n            console.log(\"\\uD83D\\uDD0D Buscando configura\\xe7\\xf5es do chat do Firestore para debug...\");\n            try {\n                // Importar Firebase dinamicamente para evitar problemas de SSR\n                const { doc, getDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                const { db } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\"));\n                const chatDocRef = doc(db, \"usuarios\", requestData.username, \"conversas\", requestData.chatId);\n                const chatDocSnap = await getDoc(chatDocRef);\n                if (chatDocSnap.exists()) {\n                    var _chatConfig_systemPrompt, _chatConfig_systemPrompt1, _chatConfig_systemPrompt2, _chatConfig_context, _chatConfig_context1, _chatConfig_context2;\n                    const chatConfig = chatDocSnap.data();\n                    console.group(\"⚙️ CONFIGURA\\xc7\\xd5ES DO CHAT (FIRESTORE)\");\n                    console.log(\"\\uD83C\\uDFAF System Prompt:\", {\n                        length: ((_chatConfig_systemPrompt = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt === void 0 ? void 0 : _chatConfig_systemPrompt.length) || 0,\n                        content: chatConfig.systemPrompt || \"(vazio)\",\n                        preview: ((_chatConfig_systemPrompt1 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt1 === void 0 ? void 0 : _chatConfig_systemPrompt1.substring(0, 200)) + (((_chatConfig_systemPrompt2 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt2 === void 0 ? void 0 : _chatConfig_systemPrompt2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83D\\uDCDD Contexto:\", {\n                        length: ((_chatConfig_context = chatConfig.context) === null || _chatConfig_context === void 0 ? void 0 : _chatConfig_context.length) || 0,\n                        content: chatConfig.context || \"(vazio)\",\n                        preview: ((_chatConfig_context1 = chatConfig.context) === null || _chatConfig_context1 === void 0 ? void 0 : _chatConfig_context1.substring(0, 200)) + (((_chatConfig_context2 = chatConfig.context) === null || _chatConfig_context2 === void 0 ? void 0 : _chatConfig_context2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83C\\uDF21️ Par\\xe2metros de Gera\\xe7\\xe3o:\", {\n                        temperature: chatConfig.temperature,\n                        temperatureType: typeof chatConfig.temperature,\n                        frequencyPenalty: chatConfig.frequencyPenalty,\n                        frequencyPenaltyType: typeof chatConfig.frequencyPenalty,\n                        repetitionPenalty: chatConfig.repetitionPenalty,\n                        repetitionPenaltyType: typeof chatConfig.repetitionPenalty,\n                        maxTokens: chatConfig.maxTokens,\n                        maxTokensType: typeof chatConfig.maxTokens,\n                        latexInstructions: chatConfig.latexInstructions\n                    });\n                    console.log(\"\\uD83D\\uDCCA Metadados:\", {\n                        lastUsedModel: chatConfig.lastUsedModel,\n                        createdAt: chatConfig.createdAt,\n                        lastUpdatedAt: chatConfig.lastUpdatedAt,\n                        name: chatConfig.name\n                    });\n                    console.groupEnd();\n                } else {\n                    console.warn(\"⚠️ Documento do chat n\\xe3o encontrado no Firestore\");\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar configura\\xe7\\xf5es do chat do Firestore:\", error);\n            }\n            // Buscar histórico de mensagens do Storage para mostrar nos logs\n            console.log(\"\\uD83D\\uDD0D Buscando hist\\xf3rico de mensagens do Storage para debug...\");\n            try {\n                const messagesResponse = await fetch(\"/api/chat/\".concat(requestData.username, \"/\").concat(requestData.chatId));\n                if (messagesResponse.ok) {\n                    const chatData = await messagesResponse.json();\n                    const messages = chatData.messages || [];\n                    console.group(\"\\uD83D\\uDCAC HIST\\xd3RICO DE MENSAGENS (STORAGE)\");\n                    console.log(\"\\uD83D\\uDCCA Total de mensagens:\", messages.length);\n                    if (messages.length > 0) {\n                        var _messages_, _messages_1;\n                        console.log(\"\\uD83D\\uDCCB \\xdaltimas 5 mensagens:\", messages.slice(-5).map((msg, index)=>{\n                            var _msg_content, _msg_content1, _msg_content2, _msg_attachments;\n                            return {\n                                index: messages.length - 5 + index,\n                                id: msg.id,\n                                role: msg.role,\n                                contentLength: ((_msg_content = msg.content) === null || _msg_content === void 0 ? void 0 : _msg_content.length) || 0,\n                                contentPreview: ((_msg_content1 = msg.content) === null || _msg_content1 === void 0 ? void 0 : _msg_content1.substring(0, 100)) + (((_msg_content2 = msg.content) === null || _msg_content2 === void 0 ? void 0 : _msg_content2.length) > 100 ? \"...\" : \"\"),\n                                timestamp: msg.timestamp,\n                                hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                                attachmentCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0\n                            };\n                        }));\n                        if (messages.length > 5) {\n                            console.log(\"\\uD83D\\uDCDC Mensagens mais antigas:\", \"\".concat(messages.length - 5, \" mensagens anteriores n\\xe3o mostradas\"));\n                        }\n                        // Estatísticas do histórico\n                        const userMessages = messages.filter((msg)=>msg.role === \"user\").length;\n                        const assistantMessages = messages.filter((msg)=>msg.role === \"assistant\").length;\n                        const totalChars = messages.reduce((acc, msg)=>{\n                            var _msg_content;\n                            return acc + (((_msg_content = msg.content) === null || _msg_content === void 0 ? void 0 : _msg_content.length) || 0);\n                        }, 0);\n                        console.log(\"\\uD83D\\uDCC8 Estat\\xedsticas do Hist\\xf3rico:\", {\n                            totalMessages: messages.length,\n                            userMessages,\n                            assistantMessages,\n                            totalCharacters: totalChars,\n                            averageMessageLength: messages.length > 0 ? Math.round(totalChars / messages.length) : 0,\n                            oldestMessage: (_messages_ = messages[0]) === null || _messages_ === void 0 ? void 0 : _messages_.timestamp,\n                            newestMessage: (_messages_1 = messages[messages.length - 1]) === null || _messages_1 === void 0 ? void 0 : _messages_1.timestamp\n                        });\n                    } else {\n                        console.log(\"\\uD83D\\uDCED Nenhuma mensagem encontrada no hist\\xf3rico\");\n                    }\n                    console.groupEnd();\n                } else {\n                    console.warn(\"⚠️ Falha ao buscar mensagens - Status:\", messagesResponse.status);\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar hist\\xf3rico de mensagens:\", error);\n            }\n            console.groupEnd();\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            // DEBUG: Log da resposta\n            console.group(\"\\uD83D\\uDCE1 AI SERVICE - RESPOSTA RECEBIDA\");\n            console.log(\"✅ Status:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCCB Headers:\", Object.fromEntries(response.headers.entries()));\n            console.log(\"\\uD83D\\uDD0D Response OK:\", response.ok);\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            console.group(\"\\uD83D\\uDD04 AI SERVICE - PROCESSANDO STREAM\");\n            console.log(\"\\uD83D\\uDCD6 Reader criado:\", !!reader);\n            console.log(\"⏰ In\\xedcio do stream:\", new Date().toISOString());\n            let chunkCount = 0;\n            let totalBytes = 0;\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        console.log(\"✅ Stream finalizado\");\n                        console.log(\"\\uD83D\\uDCCA Estat\\xedsticas finais:\", {\n                            totalChunks: chunkCount,\n                            totalBytes: totalBytes,\n                            responseLength: fullResponse.length,\n                            avgChunkSize: chunkCount > 0 ? Math.round(totalBytes / chunkCount) : 0\n                        });\n                        console.groupEnd();\n                        break;\n                    }\n                    chunkCount++;\n                    totalBytes += (value === null || value === void 0 ? void 0 : value.length) || 0;\n                    // DEBUG: Log detalhado dos bytes brutos\n                    if (chunkCount <= 10) {\n                        console.log(\"\\uD83D\\uDD0D Chunk \".concat(chunkCount, \" - Bytes brutos:\"), {\n                            rawBytes: Array.from(value || []).map((b)=>b.toString(16).padStart(2, \"0\")).join(\" \"),\n                            byteLength: (value === null || value === void 0 ? void 0 : value.length) || 0,\n                            firstBytes: Array.from((value === null || value === void 0 ? void 0 : value.slice(0, 10)) || []),\n                            lastBytes: Array.from((value === null || value === void 0 ? void 0 : value.slice(-10)) || [])\n                        });\n                    }\n                    let chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    // CORREÇÃO: Limpar caracteres problemáticos\n                    const originalChunk = chunk;\n                    chunk = chunk.replace(/[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]/g, \"\") // Remove caracteres de controle\n                    .replace(/\\u00A0/g, \" \") // Substitui espaços não-quebráveis por espaços normais\n                    .replace(/\\uFFFD/g, \"\"); // Remove caracteres de substituição (corrupção)\n                    if (originalChunk !== chunk) {\n                        console.warn(\"\\uD83E\\uDDF9 Chunk \".concat(chunkCount, \" limpo:\"), {\n                            original: originalChunk,\n                            cleaned: chunk,\n                            removedChars: Array.from(originalChunk).filter((char, i)=>char !== chunk[i]).map((c)=>c.charCodeAt(0))\n                        });\n                    }\n                    // DEBUG: Log detalhado do chunk decodificado\n                    if (chunkCount <= 10) {\n                        console.log(\"\\uD83D\\uDCE6 Chunk \".concat(chunkCount, \" - Decodificado:\"), {\n                            size: chunk.length,\n                            content: chunk,\n                            contentPreview: chunk.substring(0, 100) + (chunk.length > 100 ? \"...\" : \"\"),\n                            charCodes: Array.from(chunk).map((c)=>c.charCodeAt(0)),\n                            hasNonAscii: /[^\\x00-\\x7F]/.test(chunk),\n                            hasControlChars: /[\\x00-\\x1F\\x7F-\\x9F]/.test(chunk),\n                            timestamp: new Date().toISOString()\n                        });\n                    }\n                    fullResponse += chunk;\n                    // DEBUG: Detectar conteúdo suspeito\n                    const suspiciousPatterns = [\n                        /[\\u4e00-\\u9fff]/,\n                        /[\\u3040-\\u309f\\u30a0-\\u30ff]/,\n                        /[\\u0400-\\u04ff]/,\n                        /[\\u0590-\\u05ff]/,\n                        /[\\u0600-\\u06ff]/,\n                        /[^\\x00-\\x7F\\u00C0-\\u017F\\u0100-\\u024F]/,\n                        /\\uFFFD/,\n                        /[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]/ // Caracteres de controle\n                    ];\n                    const hasSuspiciousContent = suspiciousPatterns.some((pattern)=>pattern.test(chunk));\n                    if (hasSuspiciousContent) {\n                        console.group(\"\\uD83D\\uDEA8 CONTE\\xdaDO SUSPEITO DETECTADO!\");\n                        console.error(\"Chunk suspeito:\", chunk);\n                        console.error(\"Chunk number:\", chunkCount);\n                        console.error(\"Bytes brutos:\", Array.from(value || []).map((b)=>b.toString(16).padStart(2, \"0\")).join(\" \"));\n                        console.error(\"Char codes:\", Array.from(chunk).map((c)=>c.charCodeAt(0)));\n                        console.error(\"Full response at\\xe9 agora:\", fullResponse);\n                        console.groupEnd();\n                    }\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                console.group(\"✅ AI SERVICE - RESPOSTA COMPLETA\");\n                console.log(\"\\uD83D\\uDCDD Resposta final:\", {\n                    length: fullResponse.length,\n                    preview: fullResponse.substring(0, 200) + (fullResponse.length > 200 ? \"...\" : \"\"),\n                    wordCount: fullResponse.split(\" \").length,\n                    timestamp: new Date().toISOString()\n                });\n                console.groupEnd();\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            console.group(\"❌ AI SERVICE - ERRO CAPTURADO\");\n            console.error(\"\\uD83D\\uDEA8 Tipo do erro:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.error(\"\\uD83D\\uDCDD Mensagem:\", error instanceof Error ? error.message : String(error));\n            console.error(\"\\uD83D\\uDD0D Stack trace:\", error instanceof Error ? error.stack : \"N/A\");\n            console.error(\"⏰ Timestamp:\", new Date().toISOString());\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    console.log(\"\\uD83D\\uDED1 Requisi\\xe7\\xe3o cancelada pelo usu\\xe1rio\");\n                    console.groupEnd();\n                    return;\n                }\n                console.error(\"\\uD83D\\uDD27 Detalhes completos:\", error);\n                console.groupEnd();\n                onError(error.message);\n            } else {\n                console.error(\"\\uD83D\\uDD27 Detalhes completos:\", error);\n                console.groupEnd();\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        var _config_message;\n        console.group(\"\\uD83D\\uDE80 AI SERVICE - SEND MESSAGE SAFE\");\n        console.log(\"\\uD83D\\uDD27 Config recebida:\", {\n            username: config.username,\n            chatId: config.chatId,\n            messageLength: ((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.length) || 0,\n            model: config.model,\n            timestamp: new Date().toISOString()\n        });\n        console.log(\"\\uD83D\\uDCCB Callbacks:\", {\n            hasOnChunk: typeof onChunk === \"function\",\n            hasOnComplete: typeof onComplete === \"function\",\n            hasOnError: typeof onError === \"function\"\n        });\n        try {\n            console.log(\"✅ Validando configura\\xe7\\xe3o...\");\n            this.validateConfig(config);\n            console.log(\"✅ Configura\\xe7\\xe3o v\\xe1lida, enviando mensagem...\");\n            await this.sendMessage(config, onChunk, onComplete, onError);\n            console.log(\"✅ Mensagem enviada com sucesso\");\n            console.groupEnd();\n        } catch (error) {\n            console.group(\"❌ ERRO EM SEND MESSAGE SAFE\");\n            console.error(\"\\uD83D\\uDEA8 Erro capturado:\", error);\n            console.error(\"\\uD83D\\uDCDD Tipo:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.groupEnd();\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n            console.groupEnd();\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc2VydmljZXMvYWlTZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSx1REFBdUQ7QUF1QnZELE1BQU1BO0lBSUo7O0dBRUMsR0FDRCxNQUFNQyxZQUNKQyxNQUF1QixFQUN2QkMsT0FBZ0MsRUFDaENDLFVBQTBDLEVBQzFDQyxPQUFnQyxFQUNqQjtRQUNmLElBQUk7Z0JBbUJlQyxzQkFDQ0EsdUJBQTBDQTtZQW5CNUQsa0RBQWtEO1lBQ2xELElBQUksQ0FBQ0MsZUFBZSxHQUFHLElBQUlDO1lBRTNCLCtCQUErQjtZQUMvQixNQUFNRixjQUFjO2dCQUNsQkcsVUFBVVAsT0FBT08sUUFBUTtnQkFDekJDLFFBQVFSLE9BQU9RLE1BQU07Z0JBQ3JCQyxTQUFTVCxPQUFPUyxPQUFPO2dCQUN2QkMsT0FBT1YsT0FBT1UsS0FBSztZQUNyQjtZQUVBLCtDQUErQztZQUMvQ0MsUUFBUUMsS0FBSyxDQUFDO1lBQ2RELFFBQVFFLEdBQUcsQ0FBQyxpQ0FBdUIsSUFBSSxDQUFDQyxXQUFXO1lBQ25ESCxRQUFRRSxHQUFHLENBQUMsMkNBQTJCO2dCQUNyQ04sVUFBVUgsWUFBWUcsUUFBUTtnQkFDOUJDLFFBQVFKLFlBQVlJLE1BQU07Z0JBQzFCQyxTQUFTTCxZQUFZSyxPQUFPO2dCQUM1Qk0sZUFBZVgsRUFBQUEsdUJBQUFBLFlBQVlLLE9BQU8sY0FBbkJMLDJDQUFBQSxxQkFBcUJZLE1BQU0sS0FBSTtnQkFDOUNDLGdCQUFnQmIsRUFBQUEsd0JBQUFBLFlBQVlLLE9BQU8sY0FBbkJMLDRDQUFBQSxzQkFBcUJjLFNBQVMsQ0FBQyxHQUFHLFFBQVFkLENBQUFBLEVBQUFBLHdCQUFBQSxZQUFZSyxPQUFPLGNBQW5CTCw0Q0FBQUEsc0JBQXFCWSxNQUFNLElBQUcsTUFBTSxRQUFRLEVBQUM7Z0JBQ3ZHTixPQUFPTixZQUFZTSxLQUFLO2dCQUN4QlMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ25DO1lBQ0FWLFFBQVFFLEdBQUcsQ0FBQyx5QkFBZTtnQkFDekIsZ0JBQWdCO2dCQUNoQixVQUFVO1lBQ1o7WUFDQUYsUUFBUUUsR0FBRyxDQUFDLDJCQUFpQlMsS0FBS0MsU0FBUyxDQUFDbkIsYUFBYSxNQUFNO1lBQy9ETyxRQUFRRSxHQUFHLENBQUMsaUNBQW9CO2dCQUM5QlcsVUFBVUYsS0FBS0MsU0FBUyxDQUFDbkIsYUFBYVksTUFBTSxHQUFHO2dCQUMvQ1Msb0JBQW9CLENBQUMsQ0FBQyxJQUFJLENBQUNwQixlQUFlO2dCQUMxQ3FCLFdBQVdDLFVBQVVELFNBQVM7WUFDaEM7WUFFQSxrRUFBa0U7WUFDbEVmLFFBQVFFLEdBQUcsQ0FBQztZQUNaLElBQUk7Z0JBQ0YsK0RBQStEO2dCQUMvRCxNQUFNLEVBQUVlLEdBQUcsRUFBRUMsTUFBTSxFQUFFLEdBQUcsTUFBTSw2TEFBTztnQkFDckMsTUFBTSxFQUFFQyxFQUFFLEVBQUUsR0FBRyxNQUFNLHVKQUFPO2dCQUU1QixNQUFNQyxhQUFhSCxJQUFJRSxJQUFJLFlBQVkxQixZQUFZRyxRQUFRLEVBQUUsYUFBYUgsWUFBWUksTUFBTTtnQkFDNUYsTUFBTXdCLGNBQWMsTUFBTUgsT0FBT0U7Z0JBRWpDLElBQUlDLFlBQVlDLE1BQU0sSUFBSTt3QkFJZEMsMEJBRUNBLDJCQUE4Q0EsMkJBRy9DQSxxQkFFQ0Esc0JBQXlDQTtvQkFWcEQsTUFBTUEsYUFBYUYsWUFBWUcsSUFBSTtvQkFDbkN4QixRQUFRQyxLQUFLLENBQUM7b0JBQ2RELFFBQVFFLEdBQUcsQ0FBQywrQkFBcUI7d0JBQy9CRyxRQUFRa0IsRUFBQUEsMkJBQUFBLFdBQVdFLFlBQVksY0FBdkJGLCtDQUFBQSx5QkFBeUJsQixNQUFNLEtBQUk7d0JBQzNDcUIsU0FBU0gsV0FBV0UsWUFBWSxJQUFJO3dCQUNwQ0UsU0FBU0osRUFBQUEsNEJBQUFBLFdBQVdFLFlBQVksY0FBdkJGLGdEQUFBQSwwQkFBeUJoQixTQUFTLENBQUMsR0FBRyxRQUFRZ0IsQ0FBQUEsRUFBQUEsNEJBQUFBLFdBQVdFLFlBQVksY0FBdkJGLGdEQUFBQSwwQkFBeUJsQixNQUFNLElBQUcsTUFBTSxRQUFRLEVBQUM7b0JBQzFHO29CQUNBTCxRQUFRRSxHQUFHLENBQUMsMEJBQWdCO3dCQUMxQkcsUUFBUWtCLEVBQUFBLHNCQUFBQSxXQUFXSyxPQUFPLGNBQWxCTCwwQ0FBQUEsb0JBQW9CbEIsTUFBTSxLQUFJO3dCQUN0Q3FCLFNBQVNILFdBQVdLLE9BQU8sSUFBSTt3QkFDL0JELFNBQVNKLEVBQUFBLHVCQUFBQSxXQUFXSyxPQUFPLGNBQWxCTCwyQ0FBQUEscUJBQW9CaEIsU0FBUyxDQUFDLEdBQUcsUUFBUWdCLENBQUFBLEVBQUFBLHVCQUFBQSxXQUFXSyxPQUFPLGNBQWxCTCwyQ0FBQUEscUJBQW9CbEIsTUFBTSxJQUFHLE1BQU0sUUFBUSxFQUFDO29CQUNoRztvQkFDQUwsUUFBUUUsR0FBRyxDQUFDLGlEQUE4Qjt3QkFDeEMyQixhQUFhTixXQUFXTSxXQUFXO3dCQUNuQ0MsaUJBQWlCLE9BQU9QLFdBQVdNLFdBQVc7d0JBQzlDRSxrQkFBa0JSLFdBQVdRLGdCQUFnQjt3QkFDN0NDLHNCQUFzQixPQUFPVCxXQUFXUSxnQkFBZ0I7d0JBQ3hERSxtQkFBbUJWLFdBQVdVLGlCQUFpQjt3QkFDL0NDLHVCQUF1QixPQUFPWCxXQUFXVSxpQkFBaUI7d0JBQzFERSxXQUFXWixXQUFXWSxTQUFTO3dCQUMvQkMsZUFBZSxPQUFPYixXQUFXWSxTQUFTO3dCQUMxQ0UsbUJBQW1CZCxXQUFXYyxpQkFBaUI7b0JBQ2pEO29CQUNBckMsUUFBUUUsR0FBRyxDQUFDLDJCQUFpQjt3QkFDM0JvQyxlQUFlZixXQUFXZSxhQUFhO3dCQUN2Q0MsV0FBV2hCLFdBQVdnQixTQUFTO3dCQUMvQkMsZUFBZWpCLFdBQVdpQixhQUFhO3dCQUN2Q0MsTUFBTWxCLFdBQVdrQixJQUFJO29CQUN2QjtvQkFDQXpDLFFBQVEwQyxRQUFRO2dCQUNsQixPQUFPO29CQUNMMUMsUUFBUTJDLElBQUksQ0FBQztnQkFDZjtZQUNGLEVBQUUsT0FBT0MsT0FBTztnQkFDZDVDLFFBQVEyQyxJQUFJLENBQUMsOEVBQWtFQztZQUNqRjtZQUVBLGlFQUFpRTtZQUNqRTVDLFFBQVFFLEdBQUcsQ0FBQztZQUNaLElBQUk7Z0JBQ0YsTUFBTTJDLG1CQUFtQixNQUFNQyxNQUFNLGFBQXFDckQsT0FBeEJBLFlBQVlHLFFBQVEsRUFBQyxLQUFzQixPQUFuQkgsWUFBWUksTUFBTTtnQkFDNUYsSUFBSWdELGlCQUFpQkUsRUFBRSxFQUFFO29CQUN2QixNQUFNQyxXQUFXLE1BQU1ILGlCQUFpQkksSUFBSTtvQkFDNUMsTUFBTUMsV0FBV0YsU0FBU0UsUUFBUSxJQUFJLEVBQUU7b0JBQ3hDbEQsUUFBUUMsS0FBSyxDQUFDO29CQUNkRCxRQUFRRSxHQUFHLENBQUMsb0NBQTBCZ0QsU0FBUzdDLE1BQU07b0JBRXJELElBQUk2QyxTQUFTN0MsTUFBTSxHQUFHLEdBQUc7NEJBMkJONkMsWUFDQUE7d0JBM0JqQmxELFFBQVFFLEdBQUcsQ0FBQyx3Q0FBMkJnRCxTQUFTQyxLQUFLLENBQUMsQ0FBQyxHQUFHQyxHQUFHLENBQUMsQ0FBQ0MsS0FBVUM7Z0NBSXhERCxjQUNDQSxlQUFrQ0EsZUFHakNBO21DQVJ5RTtnQ0FDMUZDLE9BQU9KLFNBQVM3QyxNQUFNLEdBQUcsSUFBSWlEO2dDQUM3QkMsSUFBSUYsSUFBSUUsRUFBRTtnQ0FDVkMsTUFBTUgsSUFBSUcsSUFBSTtnQ0FDZEMsZUFBZUosRUFBQUEsZUFBQUEsSUFBSTNCLE9BQU8sY0FBWDJCLG1DQUFBQSxhQUFhaEQsTUFBTSxLQUFJO2dDQUN0Q3FELGdCQUFnQkwsRUFBQUEsZ0JBQUFBLElBQUkzQixPQUFPLGNBQVgyQixvQ0FBQUEsY0FBYTlDLFNBQVMsQ0FBQyxHQUFHLFFBQVE4QyxDQUFBQSxFQUFBQSxnQkFBQUEsSUFBSTNCLE9BQU8sY0FBWDJCLG9DQUFBQSxjQUFhaEQsTUFBTSxJQUFHLE1BQU0sUUFBUSxFQUFDO2dDQUN2RkcsV0FBVzZDLElBQUk3QyxTQUFTO2dDQUN4Qm1ELGdCQUFnQixDQUFDLENBQUVOLENBQUFBLElBQUlPLFdBQVcsSUFBSVAsSUFBSU8sV0FBVyxDQUFDdkQsTUFBTSxHQUFHO2dDQUMvRHdELGlCQUFpQlIsRUFBQUEsbUJBQUFBLElBQUlPLFdBQVcsY0FBZlAsdUNBQUFBLGlCQUFpQmhELE1BQU0sS0FBSTs0QkFDOUM7O3dCQUVBLElBQUk2QyxTQUFTN0MsTUFBTSxHQUFHLEdBQUc7NEJBQ3ZCTCxRQUFRRSxHQUFHLENBQUMsd0NBQThCLEdBQXVCLE9BQXBCZ0QsU0FBUzdDLE1BQU0sR0FBRyxHQUFFO3dCQUNuRTt3QkFFQSw0QkFBNEI7d0JBQzVCLE1BQU15RCxlQUFlWixTQUFTYSxNQUFNLENBQUMsQ0FBQ1YsTUFBYUEsSUFBSUcsSUFBSSxLQUFLLFFBQVFuRCxNQUFNO3dCQUM5RSxNQUFNMkQsb0JBQW9CZCxTQUFTYSxNQUFNLENBQUMsQ0FBQ1YsTUFBYUEsSUFBSUcsSUFBSSxLQUFLLGFBQWFuRCxNQUFNO3dCQUN4RixNQUFNNEQsYUFBYWYsU0FBU2dCLE1BQU0sQ0FBQyxDQUFDQyxLQUFhZDtnQ0FBb0JBO21DQUFQYyxNQUFPZCxDQUFBQSxFQUFBQSxlQUFBQSxJQUFJM0IsT0FBTyxjQUFYMkIsbUNBQUFBLGFBQWFoRCxNQUFNLEtBQUk7MkJBQUk7d0JBRWhHTCxRQUFRRSxHQUFHLENBQUMsaURBQWlDOzRCQUMzQ2tFLGVBQWVsQixTQUFTN0MsTUFBTTs0QkFDOUJ5RDs0QkFDQUU7NEJBQ0FLLGlCQUFpQko7NEJBQ2pCSyxzQkFBc0JwQixTQUFTN0MsTUFBTSxHQUFHLElBQUlrRSxLQUFLQyxLQUFLLENBQUNQLGFBQWFmLFNBQVM3QyxNQUFNLElBQUk7NEJBQ3ZGb0UsYUFBYSxHQUFFdkIsYUFBQUEsUUFBUSxDQUFDLEVBQUUsY0FBWEEsaUNBQUFBLFdBQWExQyxTQUFTOzRCQUNyQ2tFLGFBQWEsR0FBRXhCLGNBQUFBLFFBQVEsQ0FBQ0EsU0FBUzdDLE1BQU0sR0FBRyxFQUFFLGNBQTdCNkMsa0NBQUFBLFlBQStCMUMsU0FBUzt3QkFDekQ7b0JBQ0YsT0FBTzt3QkFDTFIsUUFBUUUsR0FBRyxDQUFDO29CQUNkO29CQUVBRixRQUFRMEMsUUFBUTtnQkFDbEIsT0FBTztvQkFDTDFDLFFBQVEyQyxJQUFJLENBQUMsMENBQTBDRSxpQkFBaUI4QixNQUFNO2dCQUNoRjtZQUNGLEVBQUUsT0FBTy9CLE9BQU87Z0JBQ2Q1QyxRQUFRMkMsSUFBSSxDQUFDLCtEQUFzREM7WUFDckU7WUFFQTVDLFFBQVEwQyxRQUFRO1lBRWhCLE1BQU1rQyxXQUFXLE1BQU05QixNQUFNLElBQUksQ0FBQzNDLFdBQVcsRUFBRTtnQkFDN0MwRSxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1wRSxLQUFLQyxTQUFTLENBQUNuQjtnQkFDckJ1RixRQUFRLElBQUksQ0FBQ3RGLGVBQWUsQ0FBQ3NGLE1BQU07WUFDckM7WUFFQSx5QkFBeUI7WUFDekJoRixRQUFRQyxLQUFLLENBQUM7WUFDZEQsUUFBUUUsR0FBRyxDQUFDLGFBQWEwRSxTQUFTRCxNQUFNLEVBQUVDLFNBQVNLLFVBQVU7WUFDN0RqRixRQUFRRSxHQUFHLENBQUMseUJBQWVnRixPQUFPQyxXQUFXLENBQUNQLFNBQVNFLE9BQU8sQ0FBQ00sT0FBTztZQUN0RXBGLFFBQVFFLEdBQUcsQ0FBQyw2QkFBbUIwRSxTQUFTN0IsRUFBRTtZQUMxQy9DLFFBQVFFLEdBQUcsQ0FBQyxxQkFBVzBFLFNBQVNTLEdBQUc7WUFDbkNyRixRQUFRMEMsUUFBUTtZQUVoQixJQUFJLENBQUNrQyxTQUFTN0IsRUFBRSxFQUFFO2dCQUNoQixNQUFNdUMsWUFBWSxNQUFNVixTQUFTM0IsSUFBSTtnQkFDckNqRCxRQUFRQyxLQUFLLENBQUM7Z0JBQ2RELFFBQVE0QyxLQUFLLENBQUMsV0FBV2dDLFNBQVNELE1BQU07Z0JBQ3hDM0UsUUFBUTRDLEtBQUssQ0FBQyxnQkFBZ0JnQyxTQUFTSyxVQUFVO2dCQUNqRGpGLFFBQVE0QyxLQUFLLENBQUMsZUFBZTBDO2dCQUM3QnRGLFFBQVEwQyxRQUFRO2dCQUNoQixNQUFNLElBQUk2QyxNQUFNRCxVQUFVMUMsS0FBSyxJQUFJLFFBQTRCZ0MsT0FBcEJBLFNBQVNELE1BQU0sRUFBQyxNQUF3QixPQUFwQkMsU0FBU0ssVUFBVTtZQUNwRjtZQUVBLElBQUksQ0FBQ0wsU0FBU0csSUFBSSxFQUFFO2dCQUNsQixNQUFNLElBQUlRLE1BQU07WUFDbEI7WUFFQSxtQkFBbUI7WUFDbkIsTUFBTUMsU0FBU1osU0FBU0csSUFBSSxDQUFDVSxTQUFTO1lBQ3RDLE1BQU1DLFVBQVUsSUFBSUM7WUFDcEIsSUFBSUMsZUFBZTtZQUVuQjVGLFFBQVFDLEtBQUssQ0FBQztZQUNkRCxRQUFRRSxHQUFHLENBQUMsK0JBQXFCLENBQUMsQ0FBQ3NGO1lBQ25DeEYsUUFBUUUsR0FBRyxDQUFDLDBCQUF1QixJQUFJTyxPQUFPQyxXQUFXO1lBRXpELElBQUltRixhQUFhO1lBQ2pCLElBQUlDLGFBQWE7WUFFakIsSUFBSTtnQkFDRixNQUFPLEtBQU07b0JBQ1gsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1SLE9BQU9TLElBQUk7b0JBRXpDLElBQUlGLE1BQU07d0JBQ1IvRixRQUFRRSxHQUFHLENBQUM7d0JBQ1pGLFFBQVFFLEdBQUcsQ0FBQyx3Q0FBMkI7NEJBQ3JDZ0csYUFBYUw7NEJBQ2JDLFlBQVlBOzRCQUNaSyxnQkFBZ0JQLGFBQWF2RixNQUFNOzRCQUNuQytGLGNBQWNQLGFBQWEsSUFBSXRCLEtBQUtDLEtBQUssQ0FBQ3NCLGFBQWFELGNBQWM7d0JBQ3ZFO3dCQUNBN0YsUUFBUTBDLFFBQVE7d0JBQ2hCO29CQUNGO29CQUVBbUQ7b0JBQ0FDLGNBQWNFLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBTzNGLE1BQU0sS0FBSTtvQkFFL0Isd0NBQXdDO29CQUN4QyxJQUFJd0YsY0FBYyxJQUFJO3dCQUNwQjdGLFFBQVFFLEdBQUcsQ0FBQyxzQkFBdUIsT0FBWDJGLFlBQVcscUJBQW1COzRCQUNwRFEsVUFBVUMsTUFBTUMsSUFBSSxDQUFDUCxTQUFTLEVBQUUsRUFBRTVDLEdBQUcsQ0FBQ29ELENBQUFBLElBQUtBLEVBQUVDLFFBQVEsQ0FBQyxJQUFJQyxRQUFRLENBQUMsR0FBRyxNQUFNQyxJQUFJLENBQUM7NEJBQ2pGQyxZQUFZWixDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU8zRixNQUFNLEtBQUk7NEJBQzdCd0csWUFBWVAsTUFBTUMsSUFBSSxDQUFDUCxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU83QyxLQUFLLENBQUMsR0FBRyxRQUFPLEVBQUU7NEJBQ2hEMkQsV0FBV1IsTUFBTUMsSUFBSSxDQUFDUCxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU83QyxLQUFLLENBQUMsQ0FBQyxRQUFPLEVBQUU7d0JBQy9DO29CQUNGO29CQUVBLElBQUk0RCxRQUFRckIsUUFBUXNCLE1BQU0sQ0FBQ2hCLE9BQU87d0JBQUVpQixRQUFRO29CQUFLO29CQUVqRCw0Q0FBNEM7b0JBQzVDLE1BQU1DLGdCQUFnQkg7b0JBQ3RCQSxRQUFRQSxNQUNMSSxPQUFPLENBQUMscUNBQXFDLElBQUksZ0NBQWdDO3FCQUNqRkEsT0FBTyxDQUFDLFdBQVcsS0FBSyx1REFBdUQ7cUJBQy9FQSxPQUFPLENBQUMsV0FBVyxLQUFLLGdEQUFnRDtvQkFFM0UsSUFBSUQsa0JBQWtCSCxPQUFPO3dCQUMzQi9HLFFBQVEyQyxJQUFJLENBQUMsc0JBQXVCLE9BQVhrRCxZQUFXLFlBQVU7NEJBQzVDdUIsVUFBVUY7NEJBQ1ZHLFNBQVNOOzRCQUNUTyxjQUFjaEIsTUFBTUMsSUFBSSxDQUFDVyxlQUFlbkQsTUFBTSxDQUFDLENBQUN3RCxNQUFNQyxJQUFNRCxTQUFTUixLQUFLLENBQUNTLEVBQUUsRUFBRXBFLEdBQUcsQ0FBQ3FFLENBQUFBLElBQUtBLEVBQUVDLFVBQVUsQ0FBQzt3QkFDdkc7b0JBQ0Y7b0JBRUEsNkNBQTZDO29CQUM3QyxJQUFJN0IsY0FBYyxJQUFJO3dCQUNwQjdGLFFBQVFFLEdBQUcsQ0FBQyxzQkFBdUIsT0FBWDJGLFlBQVcscUJBQW1COzRCQUNwRDhCLE1BQU1aLE1BQU0xRyxNQUFNOzRCQUNsQnFCLFNBQVNxRjs0QkFDVHJELGdCQUFnQnFELE1BQU14RyxTQUFTLENBQUMsR0FBRyxPQUFRd0csQ0FBQUEsTUFBTTFHLE1BQU0sR0FBRyxNQUFNLFFBQVEsRUFBQzs0QkFDekV1SCxXQUFXdEIsTUFBTUMsSUFBSSxDQUFDUSxPQUFPM0QsR0FBRyxDQUFDcUUsQ0FBQUEsSUFBS0EsRUFBRUMsVUFBVSxDQUFDOzRCQUNuREcsYUFBYSxlQUFlQyxJQUFJLENBQUNmOzRCQUNqQ2dCLGlCQUFpQix1QkFBdUJELElBQUksQ0FBQ2Y7NEJBQzdDdkcsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO3dCQUNuQztvQkFDRjtvQkFFQWtGLGdCQUFnQm1CO29CQUVoQixvQ0FBb0M7b0JBQ3BDLE1BQU1pQixxQkFBcUI7d0JBQ3pCO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBLG1DQUFtQyx5QkFBeUI7cUJBQzdEO29CQUVELE1BQU1DLHVCQUF1QkQsbUJBQW1CRSxJQUFJLENBQUNDLENBQUFBLFVBQVdBLFFBQVFMLElBQUksQ0FBQ2Y7b0JBRTdFLElBQUlrQixzQkFBc0I7d0JBQ3hCakksUUFBUUMsS0FBSyxDQUFDO3dCQUNkRCxRQUFRNEMsS0FBSyxDQUFDLG1CQUFtQm1FO3dCQUNqQy9HLFFBQVE0QyxLQUFLLENBQUMsaUJBQWlCaUQ7d0JBQy9CN0YsUUFBUTRDLEtBQUssQ0FBQyxpQkFBaUIwRCxNQUFNQyxJQUFJLENBQUNQLFNBQVMsRUFBRSxFQUFFNUMsR0FBRyxDQUFDb0QsQ0FBQUEsSUFBS0EsRUFBRUMsUUFBUSxDQUFDLElBQUlDLFFBQVEsQ0FBQyxHQUFHLE1BQU1DLElBQUksQ0FBQzt3QkFDdEczRyxRQUFRNEMsS0FBSyxDQUFDLGVBQWUwRCxNQUFNQyxJQUFJLENBQUNRLE9BQU8zRCxHQUFHLENBQUNxRSxDQUFBQSxJQUFLQSxFQUFFQyxVQUFVLENBQUM7d0JBQ3JFMUgsUUFBUTRDLEtBQUssQ0FBQywrQkFBNEJnRDt3QkFDMUM1RixRQUFRMEMsUUFBUTtvQkFDbEI7b0JBRUEsa0NBQWtDO29CQUNsQ3BELFFBQVF5SDtnQkFDVjtnQkFFQS9HLFFBQVFDLEtBQUssQ0FBQztnQkFDZEQsUUFBUUUsR0FBRyxDQUFDLGdDQUFzQjtvQkFDaENHLFFBQVF1RixhQUFhdkYsTUFBTTtvQkFDM0JzQixTQUFTaUUsYUFBYXJGLFNBQVMsQ0FBQyxHQUFHLE9BQVFxRixDQUFBQSxhQUFhdkYsTUFBTSxHQUFHLE1BQU0sUUFBUSxFQUFDO29CQUNoRitILFdBQVd4QyxhQUFheUMsS0FBSyxDQUFDLEtBQUtoSSxNQUFNO29CQUN6Q0csV0FBVyxJQUFJQyxPQUFPQyxXQUFXO2dCQUNuQztnQkFDQVYsUUFBUTBDLFFBQVE7Z0JBRWhCLCtCQUErQjtnQkFDL0JuRCxXQUFXcUc7WUFFYixTQUFVO2dCQUNSSixPQUFPOEMsV0FBVztZQUNwQjtRQUVGLEVBQUUsT0FBTzFGLE9BQU87WUFDZDVDLFFBQVFDLEtBQUssQ0FBQztZQUNkRCxRQUFRNEMsS0FBSyxDQUFDLDhCQUFvQkEsaUJBQWlCMkMsUUFBUTNDLE1BQU0yRixXQUFXLENBQUM5RixJQUFJLEdBQUcsT0FBT0c7WUFDM0Y1QyxRQUFRNEMsS0FBSyxDQUFDLDBCQUFnQkEsaUJBQWlCMkMsUUFBUTNDLE1BQU05QyxPQUFPLEdBQUcwSSxPQUFPNUY7WUFDOUU1QyxRQUFRNEMsS0FBSyxDQUFDLDZCQUFtQkEsaUJBQWlCMkMsUUFBUTNDLE1BQU02RixLQUFLLEdBQUc7WUFDeEV6SSxRQUFRNEMsS0FBSyxDQUFDLGdCQUFnQixJQUFJbkMsT0FBT0MsV0FBVztZQUVwRCxJQUFJa0MsaUJBQWlCMkMsT0FBTztnQkFDMUIsSUFBSTNDLE1BQU1ILElBQUksS0FBSyxjQUFjO29CQUMvQnpDLFFBQVFFLEdBQUcsQ0FBQztvQkFDWkYsUUFBUTBDLFFBQVE7b0JBQ2hCO2dCQUNGO2dCQUNBMUMsUUFBUTRDLEtBQUssQ0FBQyxvQ0FBMEJBO2dCQUN4QzVDLFFBQVEwQyxRQUFRO2dCQUNoQmxELFFBQVFvRCxNQUFNOUMsT0FBTztZQUN2QixPQUFPO2dCQUNMRSxRQUFRNEMsS0FBSyxDQUFDLG9DQUEwQkE7Z0JBQ3hDNUMsUUFBUTBDLFFBQVE7Z0JBQ2hCbEQsUUFBUTtZQUNWO1FBQ0YsU0FBVTtZQUNSLElBQUksQ0FBQ0UsZUFBZSxHQUFHO1FBQ3pCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNEZ0osZ0JBQXNCO1FBQ3BCLElBQUksSUFBSSxDQUFDaEosZUFBZSxFQUFFO1lBQ3hCLElBQUksQ0FBQ0EsZUFBZSxDQUFDaUosS0FBSztZQUMxQixJQUFJLENBQUNqSixlQUFlLEdBQUc7UUFDekI7SUFDRjtJQUVBOztHQUVDLEdBQ0RrSixzQkFBK0I7UUFDN0IsT0FBTyxJQUFJLENBQUNsSixlQUFlLEtBQUs7SUFDbEM7SUFFQTs7R0FFQyxHQUNELE1BQU1tSixpQkFBaUJqSixRQUFnQixFQUFFQyxNQUFjLEVBQTBCO1FBQy9FLElBQUk7WUFDRixNQUFNK0UsV0FBVyxNQUFNOUIsTUFBTSxhQUF5QmpELE9BQVpELFVBQVMsS0FBVSxPQUFQQyxTQUFVO2dCQUM5RGdGLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtZQUNGO1lBRUEsSUFBSSxDQUFDRixTQUFTN0IsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUl3QyxNQUFNLDBCQUE4QyxPQUFwQlgsU0FBU0ssVUFBVTtZQUMvRDtZQUVBLE1BQU1qQyxXQUFXLE1BQU00QixTQUFTM0IsSUFBSTtZQUNwQyxPQUFPRCxTQUFTRSxRQUFRLElBQUksRUFBRTtRQUVoQyxFQUFFLE9BQU9OLE9BQU87WUFDZDVDLFFBQVE0QyxLQUFLLENBQUMsdUNBQXVDQTtZQUNyRCxPQUFPLEVBQUU7UUFDWDtJQUNGO0lBRUE7O0dBRUMsR0FDRGtHLGtCQUFrQjVGLFFBQWUsRUFBaUI7UUFDaEQsT0FBT0EsU0FBU0UsR0FBRyxDQUFDQyxDQUFBQSxNQUFRO2dCQUMxQkUsSUFBSUYsSUFBSUUsRUFBRTtnQkFDVjdCLFNBQVMyQixJQUFJM0IsT0FBTztnQkFDcEI4QixNQUFNSCxJQUFJMEYsTUFBTSxLQUFLLFNBQVMsU0FBUztnQkFDdkN2SSxXQUFXNkMsSUFBSTdDLFNBQVM7Z0JBQ3hCd0ksWUFBWTNGLElBQUkyRixVQUFVLElBQUk7WUFDaEM7SUFDRjtJQUVBOztHQUVDLEdBQ0RDLG9CQUFvQi9GLFFBQXVCLEVBQVM7UUFDbEQsT0FBT0EsU0FBU0UsR0FBRyxDQUFDQyxDQUFBQSxNQUFRO2dCQUMxQkUsSUFBSUYsSUFBSUUsRUFBRTtnQkFDVjdCLFNBQVMyQixJQUFJM0IsT0FBTztnQkFDcEJxSCxRQUFRMUYsSUFBSUcsSUFBSSxLQUFLLFNBQVMsU0FBUztnQkFDdkNoRCxXQUFXNkMsSUFBSTdDLFNBQVM7Z0JBQ3hCd0ksWUFBWTNGLElBQUkyRixVQUFVLElBQUk7WUFDaEM7SUFDRjtJQUVBOztHQUVDLEdBQ0RFLG9CQUE0QjtRQUMxQixPQUFPekksS0FBSzBJLEdBQUcsR0FBRzFDLFFBQVEsQ0FBQyxNQUFNbEMsS0FBSzZFLE1BQU0sR0FBRzNDLFFBQVEsQ0FBQyxJQUFJbEcsU0FBUyxDQUFDO0lBQ3hFO0lBRUE7O0dBRUMsR0FDRCxlQUF1QmxCLE1BQXVCLEVBQVE7WUFDL0NBLGtCQUdBQSxnQkFHQUE7UUFOTCxJQUFJLEdBQUNBLG1CQUFBQSxPQUFPTyxRQUFRLGNBQWZQLHVDQUFBQSxpQkFBaUJpSyxJQUFJLEtBQUk7WUFDNUIsTUFBTSxJQUFJL0QsTUFBTTtRQUNsQjtRQUNBLElBQUksR0FBQ2xHLGlCQUFBQSxPQUFPUSxNQUFNLGNBQWJSLHFDQUFBQSxlQUFlaUssSUFBSSxLQUFJO1lBQzFCLE1BQU0sSUFBSS9ELE1BQU07UUFDbEI7UUFDQSxJQUFJLEdBQUNsRyxrQkFBQUEsT0FBT1MsT0FBTyxjQUFkVCxzQ0FBQUEsZ0JBQWdCaUssSUFBSSxLQUFJO1lBQzNCLE1BQU0sSUFBSS9ELE1BQU07UUFDbEI7UUFDQSxJQUFJbEcsT0FBT1MsT0FBTyxDQUFDTyxNQUFNLEdBQUcsT0FBTztZQUNqQyxNQUFNLElBQUlrRixNQUFNO1FBQ2xCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1nRSxnQkFDSmxLLE1BQXVCLEVBQ3ZCQyxPQUFnQyxFQUNoQ0MsVUFBMEMsRUFDMUNDLE9BQWdDLEVBQ2pCO1lBS0VIO1FBSmpCVyxRQUFRQyxLQUFLLENBQUM7UUFDZEQsUUFBUUUsR0FBRyxDQUFDLGlDQUF1QjtZQUNqQ04sVUFBVVAsT0FBT08sUUFBUTtZQUN6QkMsUUFBUVIsT0FBT1EsTUFBTTtZQUNyQk8sZUFBZWYsRUFBQUEsa0JBQUFBLE9BQU9TLE9BQU8sY0FBZFQsc0NBQUFBLGdCQUFnQmdCLE1BQU0sS0FBSTtZQUN6Q04sT0FBT1YsT0FBT1UsS0FBSztZQUNuQlMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1FBQ25DO1FBQ0FWLFFBQVFFLEdBQUcsQ0FBQywyQkFBaUI7WUFDM0JzSixZQUFZLE9BQU9sSyxZQUFZO1lBQy9CbUssZUFBZSxPQUFPbEssZUFBZTtZQUNyQ21LLFlBQVksT0FBT2xLLFlBQVk7UUFDakM7UUFFQSxJQUFJO1lBQ0ZRLFFBQVFFLEdBQUcsQ0FBQztZQUNaLElBQUksQ0FBQ21KLGNBQWMsQ0FBQ2hLO1lBQ3BCVyxRQUFRRSxHQUFHLENBQUM7WUFDWixNQUFNLElBQUksQ0FBQ2QsV0FBVyxDQUFDQyxRQUFRQyxTQUFTQyxZQUFZQztZQUNwRFEsUUFBUUUsR0FBRyxDQUFDO1lBQ1pGLFFBQVEwQyxRQUFRO1FBQ2xCLEVBQUUsT0FBT0UsT0FBTztZQUNkNUMsUUFBUUMsS0FBSyxDQUFDO1lBQ2RELFFBQVE0QyxLQUFLLENBQUMsZ0NBQXNCQTtZQUNwQzVDLFFBQVE0QyxLQUFLLENBQUMsc0JBQVlBLGlCQUFpQjJDLFFBQVEzQyxNQUFNMkYsV0FBVyxDQUFDOUYsSUFBSSxHQUFHLE9BQU9HO1lBQ25GNUMsUUFBUTBDLFFBQVE7WUFFaEIsSUFBSUUsaUJBQWlCMkMsT0FBTztnQkFDMUIvRixRQUFRb0QsTUFBTTlDLE9BQU87WUFDdkIsT0FBTztnQkFDTE4sUUFBUTtZQUNWO1lBQ0FRLFFBQVEwQyxRQUFRO1FBQ2xCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1pSCxjQUFjL0osUUFBZ0IsRUFBRUMsTUFBYyxFQUFFK0osU0FBaUIsRUFBb0I7UUFDekYsSUFBSTtZQUNGLE1BQU1oRixXQUFXLE1BQU05QixNQUFNLGFBQXlCakQsT0FBWkQsVUFBUyxLQUFxQmdLLE9BQWxCL0osUUFBTyxhQUFxQixPQUFWK0osWUFBYTtnQkFDbkYvRSxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUVBLElBQUksQ0FBQ0YsU0FBUzdCLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJd0MsTUFBTSw2QkFBaUQsT0FBcEJYLFNBQVNLLFVBQVU7WUFDbEU7WUFFQSxPQUFPO1FBQ1QsRUFBRSxPQUFPckMsT0FBTztZQUNkNUMsUUFBUTRDLEtBQUssQ0FBQyw2QkFBNkJBO1lBQzNDLE9BQU87UUFDVDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNaUgsY0FBY2pLLFFBQWdCLEVBQUVDLE1BQWMsRUFBRStKLFNBQWlCLEVBQUVFLFVBQWtCLEVBQW9CO1FBQzdHLElBQUk7WUFDRixNQUFNbEYsV0FBVyxNQUFNOUIsTUFBTSxhQUF5QmpELE9BQVpELFVBQVMsS0FBcUJnSyxPQUFsQi9KLFFBQU8sYUFBcUIsT0FBVitKLFlBQWE7Z0JBQ25GL0UsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNcEUsS0FBS0MsU0FBUyxDQUFDO29CQUFFYyxTQUFTb0k7Z0JBQVc7WUFDN0M7WUFFQSxJQUFJLENBQUNsRixTQUFTN0IsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUl3QyxNQUFNLCtCQUFtRCxPQUFwQlgsU0FBU0ssVUFBVTtZQUNwRTtZQUVBLE9BQU87UUFDVCxFQUFFLE9BQU9yQyxPQUFPO1lBQ2Q1QyxRQUFRNEMsS0FBSyxDQUFDLCtCQUErQkE7WUFDN0MsT0FBTztRQUNUO0lBQ0Y7O2FBMWZpQnpDLGNBQWM7YUFDdkJULGtCQUEwQzs7QUEwZnBEO0FBRUEsK0JBQStCO0FBQ3hCLE1BQU1xSyxZQUFZLElBQUk1SyxZQUFZO0FBQ3pDLCtEQUFlNEssU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvbGliL3NlcnZpY2VzL2FpU2VydmljZS50cz82NThlIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNlcnZpw6dvIHBhcmEgaW50ZWdyYcOnw6NvIGNvbSBGaXJlYmFzZSBGdW5jdGlvbnMgZGUgSUFcblxuZXhwb3J0IGludGVyZmFjZSBDaGF0TWVzc2FnZSB7XG4gIGlkOiBzdHJpbmc7XG4gIGNvbnRlbnQ6IHN0cmluZztcbiAgcm9sZTogJ3VzZXInIHwgJ2Fzc2lzdGFudCc7XG4gIHRpbWVzdGFtcDogc3RyaW5nO1xuICBpc0Zhdm9yaXRlPzogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBTdHJlYW1SZXNwb25zZSB7XG4gIGNvbnRlbnQ6IHN0cmluZztcbiAgaXNDb21wbGV0ZTogYm9vbGVhbjtcbiAgZXJyb3I/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQUlTZXJ2aWNlQ29uZmlnIHtcbiAgdXNlcm5hbWU6IHN0cmluZztcbiAgY2hhdElkOiBzdHJpbmc7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgbW9kZWw/OiBzdHJpbmc7XG59XG5cbmNsYXNzIEFJU2VydmljZSB7XG4gIHByaXZhdGUgcmVhZG9ubHkgZnVuY3Rpb25VcmwgPSAnaHR0cHM6Ly91cy1jZW50cmFsMS1yYWZ0aG9yLTAwMDEuY2xvdWRmdW5jdGlvbnMubmV0L2NoYXRXaXRoQUknO1xuICBwcml2YXRlIGFib3J0Q29udHJvbGxlcjogQWJvcnRDb250cm9sbGVyIHwgbnVsbCA9IG51bGw7XG5cbiAgLyoqXG4gICAqIEVudmlhIG1lbnNhZ2VtIHBhcmEgYSBJQSBjb20gc3RyZWFtaW5nXG4gICAqL1xuICBhc3luYyBzZW5kTWVzc2FnZShcbiAgICBjb25maWc6IEFJU2VydmljZUNvbmZpZyxcbiAgICBvbkNodW5rOiAoY2h1bms6IHN0cmluZykgPT4gdm9pZCxcbiAgICBvbkNvbXBsZXRlOiAoZnVsbFJlc3BvbnNlOiBzdHJpbmcpID0+IHZvaWQsXG4gICAgb25FcnJvcjogKGVycm9yOiBzdHJpbmcpID0+IHZvaWRcbiAgKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIENyaWFyIG5vdm8gQWJvcnRDb250cm9sbGVyIHBhcmEgZXN0YSByZXF1aXNpw6fDo29cbiAgICAgIHRoaXMuYWJvcnRDb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xuXG4gICAgICAvLyBQcmVwYXJhciBkYWRvcyBkYSByZXF1aXNpw6fDo29cbiAgICAgIGNvbnN0IHJlcXVlc3REYXRhID0ge1xuICAgICAgICB1c2VybmFtZTogY29uZmlnLnVzZXJuYW1lLFxuICAgICAgICBjaGF0SWQ6IGNvbmZpZy5jaGF0SWQsXG4gICAgICAgIG1lc3NhZ2U6IGNvbmZpZy5tZXNzYWdlLFxuICAgICAgICBtb2RlbDogY29uZmlnLm1vZGVsLFxuICAgICAgfTtcblxuICAgICAgLy8gREVCVUc6IExvZyBkZXRhbGhhZG8gbm8gY29uc29sZSBkbyBuYXZlZ2Fkb3JcbiAgICAgIGNvbnNvbGUuZ3JvdXAoJ/CfpJYgQUkgU0VSVklDRSAtIEVOVklBTkRPIFJFUVVJU0nDh8ODTycpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6EgVVJMIGRhIEZ1bmN0aW9uOicsIHRoaXMuZnVuY3Rpb25VcmwpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk4sgRGFkb3MgZGEgUmVxdWlzacOnw6NvOicsIHtcbiAgICAgICAgdXNlcm5hbWU6IHJlcXVlc3REYXRhLnVzZXJuYW1lLFxuICAgICAgICBjaGF0SWQ6IHJlcXVlc3REYXRhLmNoYXRJZCxcbiAgICAgICAgbWVzc2FnZTogcmVxdWVzdERhdGEubWVzc2FnZSxcbiAgICAgICAgbWVzc2FnZUxlbmd0aDogcmVxdWVzdERhdGEubWVzc2FnZT8ubGVuZ3RoIHx8IDAsXG4gICAgICAgIG1lc3NhZ2VQcmV2aWV3OiByZXF1ZXN0RGF0YS5tZXNzYWdlPy5zdWJzdHJpbmcoMCwgMTAwKSArIChyZXF1ZXN0RGF0YS5tZXNzYWdlPy5sZW5ndGggPiAxMDAgPyAnLi4uJyA6ICcnKSxcbiAgICAgICAgbW9kZWw6IHJlcXVlc3REYXRhLm1vZGVsLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSk7XG4gICAgICBjb25zb2xlLmxvZygn8J+UpyBIZWFkZXJzOicsIHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgJ01ldGhvZCc6ICdQT1NUJ1xuICAgICAgfSk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpiBCb2R5IEpTT046JywgSlNPTi5zdHJpbmdpZnkocmVxdWVzdERhdGEsIG51bGwsIDIpKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OKIEVzdGF0w61zdGljYXM6Jywge1xuICAgICAgICBib2R5U2l6ZTogSlNPTi5zdHJpbmdpZnkocmVxdWVzdERhdGEpLmxlbmd0aCArICcgYnl0ZXMnLFxuICAgICAgICBoYXNBYm9ydENvbnRyb2xsZXI6ICEhdGhpcy5hYm9ydENvbnRyb2xsZXIsXG4gICAgICAgIHVzZXJBZ2VudDogbmF2aWdhdG9yLnVzZXJBZ2VudFxuICAgICAgfSk7XG5cbiAgICAgIC8vIEJ1c2NhciBjb25maWd1cmHDp8O1ZXMgZG8gY2hhdCBkbyBGaXJlc3RvcmUgcGFyYSBtb3N0cmFyIG5vcyBsb2dzXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBCdXNjYW5kbyBjb25maWd1cmHDp8O1ZXMgZG8gY2hhdCBkbyBGaXJlc3RvcmUgcGFyYSBkZWJ1Zy4uLicpO1xuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gSW1wb3J0YXIgRmlyZWJhc2UgZGluYW1pY2FtZW50ZSBwYXJhIGV2aXRhciBwcm9ibGVtYXMgZGUgU1NSXG4gICAgICAgIGNvbnN0IHsgZG9jLCBnZXREb2MgfSA9IGF3YWl0IGltcG9ydCgnZmlyZWJhc2UvZmlyZXN0b3JlJyk7XG4gICAgICAgIGNvbnN0IHsgZGIgfSA9IGF3YWl0IGltcG9ydCgnQC9saWIvZmlyZWJhc2UnKTtcblxuICAgICAgICBjb25zdCBjaGF0RG9jUmVmID0gZG9jKGRiLCAndXN1YXJpb3MnLCByZXF1ZXN0RGF0YS51c2VybmFtZSwgJ2NvbnZlcnNhcycsIHJlcXVlc3REYXRhLmNoYXRJZCk7XG4gICAgICAgIGNvbnN0IGNoYXREb2NTbmFwID0gYXdhaXQgZ2V0RG9jKGNoYXREb2NSZWYpO1xuXG4gICAgICAgIGlmIChjaGF0RG9jU25hcC5leGlzdHMoKSkge1xuICAgICAgICAgIGNvbnN0IGNoYXRDb25maWcgPSBjaGF0RG9jU25hcC5kYXRhKCk7XG4gICAgICAgICAgY29uc29sZS5ncm91cCgn4pqZ77iPIENPTkZJR1VSQcOHw5VFUyBETyBDSEFUIChGSVJFU1RPUkUpJyk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfjq8gU3lzdGVtIFByb21wdDonLCB7XG4gICAgICAgICAgICBsZW5ndGg6IGNoYXRDb25maWcuc3lzdGVtUHJvbXB0Py5sZW5ndGggfHwgMCxcbiAgICAgICAgICAgIGNvbnRlbnQ6IGNoYXRDb25maWcuc3lzdGVtUHJvbXB0IHx8ICcodmF6aW8pJyxcbiAgICAgICAgICAgIHByZXZpZXc6IGNoYXRDb25maWcuc3lzdGVtUHJvbXB0Py5zdWJzdHJpbmcoMCwgMjAwKSArIChjaGF0Q29uZmlnLnN5c3RlbVByb21wdD8ubGVuZ3RoID4gMjAwID8gJy4uLicgOiAnJylcbiAgICAgICAgICB9KTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+TnSBDb250ZXh0bzonLCB7XG4gICAgICAgICAgICBsZW5ndGg6IGNoYXRDb25maWcuY29udGV4dD8ubGVuZ3RoIHx8IDAsXG4gICAgICAgICAgICBjb250ZW50OiBjaGF0Q29uZmlnLmNvbnRleHQgfHwgJyh2YXppbyknLFxuICAgICAgICAgICAgcHJldmlldzogY2hhdENvbmZpZy5jb250ZXh0Py5zdWJzdHJpbmcoMCwgMjAwKSArIChjaGF0Q29uZmlnLmNvbnRleHQ/Lmxlbmd0aCA+IDIwMCA/ICcuLi4nIDogJycpXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ/CfjKHvuI8gUGFyw6JtZXRyb3MgZGUgR2VyYcOnw6NvOicsIHtcbiAgICAgICAgICAgIHRlbXBlcmF0dXJlOiBjaGF0Q29uZmlnLnRlbXBlcmF0dXJlLFxuICAgICAgICAgICAgdGVtcGVyYXR1cmVUeXBlOiB0eXBlb2YgY2hhdENvbmZpZy50ZW1wZXJhdHVyZSxcbiAgICAgICAgICAgIGZyZXF1ZW5jeVBlbmFsdHk6IGNoYXRDb25maWcuZnJlcXVlbmN5UGVuYWx0eSxcbiAgICAgICAgICAgIGZyZXF1ZW5jeVBlbmFsdHlUeXBlOiB0eXBlb2YgY2hhdENvbmZpZy5mcmVxdWVuY3lQZW5hbHR5LFxuICAgICAgICAgICAgcmVwZXRpdGlvblBlbmFsdHk6IGNoYXRDb25maWcucmVwZXRpdGlvblBlbmFsdHksXG4gICAgICAgICAgICByZXBldGl0aW9uUGVuYWx0eVR5cGU6IHR5cGVvZiBjaGF0Q29uZmlnLnJlcGV0aXRpb25QZW5hbHR5LFxuICAgICAgICAgICAgbWF4VG9rZW5zOiBjaGF0Q29uZmlnLm1heFRva2VucyxcbiAgICAgICAgICAgIG1heFRva2Vuc1R5cGU6IHR5cGVvZiBjaGF0Q29uZmlnLm1heFRva2VucyxcbiAgICAgICAgICAgIGxhdGV4SW5zdHJ1Y3Rpb25zOiBjaGF0Q29uZmlnLmxhdGV4SW5zdHJ1Y3Rpb25zXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfk4ogTWV0YWRhZG9zOicsIHtcbiAgICAgICAgICAgIGxhc3RVc2VkTW9kZWw6IGNoYXRDb25maWcubGFzdFVzZWRNb2RlbCxcbiAgICAgICAgICAgIGNyZWF0ZWRBdDogY2hhdENvbmZpZy5jcmVhdGVkQXQsXG4gICAgICAgICAgICBsYXN0VXBkYXRlZEF0OiBjaGF0Q29uZmlnLmxhc3RVcGRhdGVkQXQsXG4gICAgICAgICAgICBuYW1lOiBjaGF0Q29uZmlnLm5hbWVcbiAgICAgICAgICB9KTtcbiAgICAgICAgICBjb25zb2xlLmdyb3VwRW5kKCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gRG9jdW1lbnRvIGRvIGNoYXQgbsOjbyBlbmNvbnRyYWRvIG5vIEZpcmVzdG9yZScpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBOw6NvIGZvaSBwb3Nzw612ZWwgYnVzY2FyIGNvbmZpZ3VyYcOnw7VlcyBkbyBjaGF0IGRvIEZpcmVzdG9yZTonLCBlcnJvcik7XG4gICAgICB9XG5cbiAgICAgIC8vIEJ1c2NhciBoaXN0w7NyaWNvIGRlIG1lbnNhZ2VucyBkbyBTdG9yYWdlIHBhcmEgbW9zdHJhciBub3MgbG9nc1xuICAgICAgY29uc29sZS5sb2coJ/CflI0gQnVzY2FuZG8gaGlzdMOzcmljbyBkZSBtZW5zYWdlbnMgZG8gU3RvcmFnZSBwYXJhIGRlYnVnLi4uJyk7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBtZXNzYWdlc1Jlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvY2hhdC8ke3JlcXVlc3REYXRhLnVzZXJuYW1lfS8ke3JlcXVlc3REYXRhLmNoYXRJZH1gKTtcbiAgICAgICAgaWYgKG1lc3NhZ2VzUmVzcG9uc2Uub2spIHtcbiAgICAgICAgICBjb25zdCBjaGF0RGF0YSA9IGF3YWl0IG1lc3NhZ2VzUmVzcG9uc2UuanNvbigpO1xuICAgICAgICAgIGNvbnN0IG1lc3NhZ2VzID0gY2hhdERhdGEubWVzc2FnZXMgfHwgW107XG4gICAgICAgICAgY29uc29sZS5ncm91cCgn8J+SrCBISVNUw5NSSUNPIERFIE1FTlNBR0VOUyAoU1RPUkFHRSknKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+TiiBUb3RhbCBkZSBtZW5zYWdlbnM6JywgbWVzc2FnZXMubGVuZ3RoKTtcblxuICAgICAgICAgIGlmIChtZXNzYWdlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TiyDDmmx0aW1hcyA1IG1lbnNhZ2VuczonLCBtZXNzYWdlcy5zbGljZSgtNSkubWFwKChtc2c6IGFueSwgaW5kZXg6IG51bWJlcikgPT4gKHtcbiAgICAgICAgICAgICAgaW5kZXg6IG1lc3NhZ2VzLmxlbmd0aCAtIDUgKyBpbmRleCxcbiAgICAgICAgICAgICAgaWQ6IG1zZy5pZCxcbiAgICAgICAgICAgICAgcm9sZTogbXNnLnJvbGUsXG4gICAgICAgICAgICAgIGNvbnRlbnRMZW5ndGg6IG1zZy5jb250ZW50Py5sZW5ndGggfHwgMCxcbiAgICAgICAgICAgICAgY29udGVudFByZXZpZXc6IG1zZy5jb250ZW50Py5zdWJzdHJpbmcoMCwgMTAwKSArIChtc2cuY29udGVudD8ubGVuZ3RoID4gMTAwID8gJy4uLicgOiAnJyksXG4gICAgICAgICAgICAgIHRpbWVzdGFtcDogbXNnLnRpbWVzdGFtcCxcbiAgICAgICAgICAgICAgaGFzQXR0YWNobWVudHM6ICEhKG1zZy5hdHRhY2htZW50cyAmJiBtc2cuYXR0YWNobWVudHMubGVuZ3RoID4gMCksXG4gICAgICAgICAgICAgIGF0dGFjaG1lbnRDb3VudDogbXNnLmF0dGFjaG1lbnRzPy5sZW5ndGggfHwgMFxuICAgICAgICAgICAgfSkpKTtcblxuICAgICAgICAgICAgaWYgKG1lc3NhZ2VzLmxlbmd0aCA+IDUpIHtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk5wgTWVuc2FnZW5zIG1haXMgYW50aWdhczonLCBgJHttZXNzYWdlcy5sZW5ndGggLSA1fSBtZW5zYWdlbnMgYW50ZXJpb3JlcyBuw6NvIG1vc3RyYWRhc2ApO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBFc3RhdMOtc3RpY2FzIGRvIGhpc3TDs3JpY29cbiAgICAgICAgICAgIGNvbnN0IHVzZXJNZXNzYWdlcyA9IG1lc3NhZ2VzLmZpbHRlcigobXNnOiBhbnkpID0+IG1zZy5yb2xlID09PSAndXNlcicpLmxlbmd0aDtcbiAgICAgICAgICAgIGNvbnN0IGFzc2lzdGFudE1lc3NhZ2VzID0gbWVzc2FnZXMuZmlsdGVyKChtc2c6IGFueSkgPT4gbXNnLnJvbGUgPT09ICdhc3Npc3RhbnQnKS5sZW5ndGg7XG4gICAgICAgICAgICBjb25zdCB0b3RhbENoYXJzID0gbWVzc2FnZXMucmVkdWNlKChhY2M6IG51bWJlciwgbXNnOiBhbnkpID0+IGFjYyArIChtc2cuY29udGVudD8ubGVuZ3RoIHx8IDApLCAwKTtcblxuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk4ggRXN0YXTDrXN0aWNhcyBkbyBIaXN0w7NyaWNvOicsIHtcbiAgICAgICAgICAgICAgdG90YWxNZXNzYWdlczogbWVzc2FnZXMubGVuZ3RoLFxuICAgICAgICAgICAgICB1c2VyTWVzc2FnZXMsXG4gICAgICAgICAgICAgIGFzc2lzdGFudE1lc3NhZ2VzLFxuICAgICAgICAgICAgICB0b3RhbENoYXJhY3RlcnM6IHRvdGFsQ2hhcnMsXG4gICAgICAgICAgICAgIGF2ZXJhZ2VNZXNzYWdlTGVuZ3RoOiBtZXNzYWdlcy5sZW5ndGggPiAwID8gTWF0aC5yb3VuZCh0b3RhbENoYXJzIC8gbWVzc2FnZXMubGVuZ3RoKSA6IDAsXG4gICAgICAgICAgICAgIG9sZGVzdE1lc3NhZ2U6IG1lc3NhZ2VzWzBdPy50aW1lc3RhbXAsXG4gICAgICAgICAgICAgIG5ld2VzdE1lc3NhZ2U6IG1lc3NhZ2VzW21lc3NhZ2VzLmxlbmd0aCAtIDFdPy50aW1lc3RhbXBcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TrSBOZW5odW1hIG1lbnNhZ2VtIGVuY29udHJhZGEgbm8gaGlzdMOzcmljbycpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnNvbGUuZ3JvdXBFbmQoKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBGYWxoYSBhbyBidXNjYXIgbWVuc2FnZW5zIC0gU3RhdHVzOicsIG1lc3NhZ2VzUmVzcG9uc2Uuc3RhdHVzKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gTsOjbyBmb2kgcG9zc8OtdmVsIGJ1c2NhciBoaXN0w7NyaWNvIGRlIG1lbnNhZ2VuczonLCBlcnJvcik7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUuZ3JvdXBFbmQoKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCh0aGlzLmZ1bmN0aW9uVXJsLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocmVxdWVzdERhdGEpLFxuICAgICAgICBzaWduYWw6IHRoaXMuYWJvcnRDb250cm9sbGVyLnNpZ25hbCxcbiAgICAgIH0pO1xuXG4gICAgICAvLyBERUJVRzogTG9nIGRhIHJlc3Bvc3RhXG4gICAgICBjb25zb2xlLmdyb3VwKCfwn5OhIEFJIFNFUlZJQ0UgLSBSRVNQT1NUQSBSRUNFQklEQScpO1xuICAgICAgY29uc29sZS5sb2coJ+KchSBTdGF0dXM6JywgcmVzcG9uc2Uuc3RhdHVzLCByZXNwb25zZS5zdGF0dXNUZXh0KTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OLIEhlYWRlcnM6JywgT2JqZWN0LmZyb21FbnRyaWVzKHJlc3BvbnNlLmhlYWRlcnMuZW50cmllcygpKSk7XG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBSZXNwb25zZSBPSzonLCByZXNwb25zZS5vayk7XG4gICAgICBjb25zb2xlLmxvZygn8J+MkCBVUkw6JywgcmVzcG9uc2UudXJsKTtcbiAgICAgIGNvbnNvbGUuZ3JvdXBFbmQoKTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGNvbnNvbGUuZ3JvdXAoJ+KdjCBBSSBTRVJWSUNFIC0gRVJSTyBOQSBSRVNQT1NUQScpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCdTdGF0dXM6JywgcmVzcG9uc2Uuc3RhdHVzKTtcbiAgICAgICAgY29uc29sZS5lcnJvcignU3RhdHVzIFRleHQ6JywgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIERhdGE6JywgZXJyb3JEYXRhKTtcbiAgICAgICAgY29uc29sZS5ncm91cEVuZCgpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLmVycm9yIHx8IGBIVFRQICR7cmVzcG9uc2Uuc3RhdHVzfTogJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApO1xuICAgICAgfVxuXG4gICAgICBpZiAoIXJlc3BvbnNlLmJvZHkpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdSZXNwb25zZSBib2R5IGlzIG5vdCBhdmFpbGFibGUnKTtcbiAgICAgIH1cblxuICAgICAgLy8gUHJvY2Vzc2FyIHN0cmVhbVxuICAgICAgY29uc3QgcmVhZGVyID0gcmVzcG9uc2UuYm9keS5nZXRSZWFkZXIoKTtcbiAgICAgIGNvbnN0IGRlY29kZXIgPSBuZXcgVGV4dERlY29kZXIoKTtcbiAgICAgIGxldCBmdWxsUmVzcG9uc2UgPSAnJztcblxuICAgICAgY29uc29sZS5ncm91cCgn8J+UhCBBSSBTRVJWSUNFIC0gUFJPQ0VTU0FORE8gU1RSRUFNJyk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TliBSZWFkZXIgY3JpYWRvOicsICEhcmVhZGVyKTtcbiAgICAgIGNvbnNvbGUubG9nKCfij7AgSW7DrWNpbyBkbyBzdHJlYW06JywgbmV3IERhdGUoKS50b0lTT1N0cmluZygpKTtcblxuICAgICAgbGV0IGNodW5rQ291bnQgPSAwO1xuICAgICAgbGV0IHRvdGFsQnl0ZXMgPSAwO1xuXG4gICAgICB0cnkge1xuICAgICAgICB3aGlsZSAodHJ1ZSkge1xuICAgICAgICAgIGNvbnN0IHsgZG9uZSwgdmFsdWUgfSA9IGF3YWl0IHJlYWRlci5yZWFkKCk7XG5cbiAgICAgICAgICBpZiAoZG9uZSkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBTdHJlYW0gZmluYWxpemFkbycpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk4ogRXN0YXTDrXN0aWNhcyBmaW5haXM6Jywge1xuICAgICAgICAgICAgICB0b3RhbENodW5rczogY2h1bmtDb3VudCxcbiAgICAgICAgICAgICAgdG90YWxCeXRlczogdG90YWxCeXRlcyxcbiAgICAgICAgICAgICAgcmVzcG9uc2VMZW5ndGg6IGZ1bGxSZXNwb25zZS5sZW5ndGgsXG4gICAgICAgICAgICAgIGF2Z0NodW5rU2l6ZTogY2h1bmtDb3VudCA+IDAgPyBNYXRoLnJvdW5kKHRvdGFsQnl0ZXMgLyBjaHVua0NvdW50KSA6IDBcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY29uc29sZS5ncm91cEVuZCgpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgY2h1bmtDb3VudCsrO1xuICAgICAgICAgIHRvdGFsQnl0ZXMgKz0gdmFsdWU/Lmxlbmd0aCB8fCAwO1xuXG4gICAgICAgICAgLy8gREVCVUc6IExvZyBkZXRhbGhhZG8gZG9zIGJ5dGVzIGJydXRvc1xuICAgICAgICAgIGlmIChjaHVua0NvdW50IDw9IDEwKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UjSBDaHVuayAke2NodW5rQ291bnR9IC0gQnl0ZXMgYnJ1dG9zOmAsIHtcbiAgICAgICAgICAgICAgcmF3Qnl0ZXM6IEFycmF5LmZyb20odmFsdWUgfHwgW10pLm1hcChiID0+IGIudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDIsICcwJykpLmpvaW4oJyAnKSxcbiAgICAgICAgICAgICAgYnl0ZUxlbmd0aDogdmFsdWU/Lmxlbmd0aCB8fCAwLFxuICAgICAgICAgICAgICBmaXJzdEJ5dGVzOiBBcnJheS5mcm9tKHZhbHVlPy5zbGljZSgwLCAxMCkgfHwgW10pLFxuICAgICAgICAgICAgICBsYXN0Qnl0ZXM6IEFycmF5LmZyb20odmFsdWU/LnNsaWNlKC0xMCkgfHwgW10pXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBsZXQgY2h1bmsgPSBkZWNvZGVyLmRlY29kZSh2YWx1ZSwgeyBzdHJlYW06IHRydWUgfSk7XG5cbiAgICAgICAgICAvLyBDT1JSRcOHw4NPOiBMaW1wYXIgY2FyYWN0ZXJlcyBwcm9ibGVtw6F0aWNvc1xuICAgICAgICAgIGNvbnN0IG9yaWdpbmFsQ2h1bmsgPSBjaHVuaztcbiAgICAgICAgICBjaHVuayA9IGNodW5rXG4gICAgICAgICAgICAucmVwbGFjZSgvW1xceDAwLVxceDA4XFx4MEJcXHgwQ1xceDBFLVxceDFGXFx4N0ZdL2csICcnKSAvLyBSZW1vdmUgY2FyYWN0ZXJlcyBkZSBjb250cm9sZVxuICAgICAgICAgICAgLnJlcGxhY2UoL1xcdTAwQTAvZywgJyAnKSAvLyBTdWJzdGl0dWkgZXNwYcOnb3MgbsOjby1xdWVicsOhdmVpcyBwb3IgZXNwYcOnb3Mgbm9ybWFpc1xuICAgICAgICAgICAgLnJlcGxhY2UoL1xcdUZGRkQvZywgJycpOyAvLyBSZW1vdmUgY2FyYWN0ZXJlcyBkZSBzdWJzdGl0dWnDp8OjbyAoY29ycnVww6fDo28pXG5cbiAgICAgICAgICBpZiAob3JpZ2luYWxDaHVuayAhPT0gY2h1bmspIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2Fybihg8J+nuSBDaHVuayAke2NodW5rQ291bnR9IGxpbXBvOmAsIHtcbiAgICAgICAgICAgICAgb3JpZ2luYWw6IG9yaWdpbmFsQ2h1bmssXG4gICAgICAgICAgICAgIGNsZWFuZWQ6IGNodW5rLFxuICAgICAgICAgICAgICByZW1vdmVkQ2hhcnM6IEFycmF5LmZyb20ob3JpZ2luYWxDaHVuaykuZmlsdGVyKChjaGFyLCBpKSA9PiBjaGFyICE9PSBjaHVua1tpXSkubWFwKGMgPT4gYy5jaGFyQ29kZUF0KDApKVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gREVCVUc6IExvZyBkZXRhbGhhZG8gZG8gY2h1bmsgZGVjb2RpZmljYWRvXG4gICAgICAgICAgaWYgKGNodW5rQ291bnQgPD0gMTApIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5OmIENodW5rICR7Y2h1bmtDb3VudH0gLSBEZWNvZGlmaWNhZG86YCwge1xuICAgICAgICAgICAgICBzaXplOiBjaHVuay5sZW5ndGgsXG4gICAgICAgICAgICAgIGNvbnRlbnQ6IGNodW5rLFxuICAgICAgICAgICAgICBjb250ZW50UHJldmlldzogY2h1bmsuc3Vic3RyaW5nKDAsIDEwMCkgKyAoY2h1bmsubGVuZ3RoID4gMTAwID8gJy4uLicgOiAnJyksXG4gICAgICAgICAgICAgIGNoYXJDb2RlczogQXJyYXkuZnJvbShjaHVuaykubWFwKGMgPT4gYy5jaGFyQ29kZUF0KDApKSxcbiAgICAgICAgICAgICAgaGFzTm9uQXNjaWk6IC9bXlxceDAwLVxceDdGXS8udGVzdChjaHVuayksXG4gICAgICAgICAgICAgIGhhc0NvbnRyb2xDaGFyczogL1tcXHgwMC1cXHgxRlxceDdGLVxceDlGXS8udGVzdChjaHVuayksXG4gICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBmdWxsUmVzcG9uc2UgKz0gY2h1bms7XG5cbiAgICAgICAgICAvLyBERUJVRzogRGV0ZWN0YXIgY29udGXDumRvIHN1c3BlaXRvXG4gICAgICAgICAgY29uc3Qgc3VzcGljaW91c1BhdHRlcm5zID0gW1xuICAgICAgICAgICAgL1tcXHU0ZTAwLVxcdTlmZmZdLywgLy8gQ2FyYWN0ZXJlcyBjaGluZXNlc1xuICAgICAgICAgICAgL1tcXHUzMDQwLVxcdTMwOWZcXHUzMGEwLVxcdTMwZmZdLywgLy8gSmFwb27DqnMgKGhpcmFnYW5hL2thdGFrYW5hKVxuICAgICAgICAgICAgL1tcXHUwNDAwLVxcdTA0ZmZdLywgLy8gQ2lyw61saWNvXG4gICAgICAgICAgICAvW1xcdTA1OTAtXFx1MDVmZl0vLCAvLyBIZWJyYWljb1xuICAgICAgICAgICAgL1tcXHUwNjAwLVxcdTA2ZmZdLywgLy8gw4FyYWJlXG4gICAgICAgICAgICAvW15cXHgwMC1cXHg3RlxcdTAwQzAtXFx1MDE3RlxcdTAxMDAtXFx1MDI0Rl0vLCAvLyBDYXJhY3RlcmVzIG7Do28tbGF0aW5vcyBzdXNwZWl0b3NcbiAgICAgICAgICAgIC9cXHVGRkZELywgLy8gQ2FyYWN0ZXJlIGRlIHN1YnN0aXR1acOnw6NvIChpbmRpY2EgY29ycnVww6fDo28pXG4gICAgICAgICAgICAvW1xceDAwLVxceDA4XFx4MEJcXHgwQ1xceDBFLVxceDFGXFx4N0ZdLyAvLyBDYXJhY3RlcmVzIGRlIGNvbnRyb2xlXG4gICAgICAgICAgXTtcblxuICAgICAgICAgIGNvbnN0IGhhc1N1c3BpY2lvdXNDb250ZW50ID0gc3VzcGljaW91c1BhdHRlcm5zLnNvbWUocGF0dGVybiA9PiBwYXR0ZXJuLnRlc3QoY2h1bmspKTtcblxuICAgICAgICAgIGlmIChoYXNTdXNwaWNpb3VzQ29udGVudCkge1xuICAgICAgICAgICAgY29uc29sZS5ncm91cCgn8J+aqCBDT05URcOaRE8gU1VTUEVJVE8gREVURUNUQURPIScpO1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignQ2h1bmsgc3VzcGVpdG86JywgY2h1bmspO1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignQ2h1bmsgbnVtYmVyOicsIGNodW5rQ291bnQpO1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignQnl0ZXMgYnJ1dG9zOicsIEFycmF5LmZyb20odmFsdWUgfHwgW10pLm1hcChiID0+IGIudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDIsICcwJykpLmpvaW4oJyAnKSk7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdDaGFyIGNvZGVzOicsIEFycmF5LmZyb20oY2h1bmspLm1hcChjID0+IGMuY2hhckNvZGVBdCgwKSkpO1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRnVsbCByZXNwb25zZSBhdMOpIGFnb3JhOicsIGZ1bGxSZXNwb25zZSk7XG4gICAgICAgICAgICBjb25zb2xlLmdyb3VwRW5kKCk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gQ2hhbWFyIGNhbGxiYWNrIHBhcmEgY2FkYSBjaHVua1xuICAgICAgICAgIG9uQ2h1bmsoY2h1bmspO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc29sZS5ncm91cCgn4pyFIEFJIFNFUlZJQ0UgLSBSRVNQT1NUQSBDT01QTEVUQScpO1xuICAgICAgICBjb25zb2xlLmxvZygn8J+TnSBSZXNwb3N0YSBmaW5hbDonLCB7XG4gICAgICAgICAgbGVuZ3RoOiBmdWxsUmVzcG9uc2UubGVuZ3RoLFxuICAgICAgICAgIHByZXZpZXc6IGZ1bGxSZXNwb25zZS5zdWJzdHJpbmcoMCwgMjAwKSArIChmdWxsUmVzcG9uc2UubGVuZ3RoID4gMjAwID8gJy4uLicgOiAnJyksXG4gICAgICAgICAgd29yZENvdW50OiBmdWxsUmVzcG9uc2Uuc3BsaXQoJyAnKS5sZW5ndGgsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnNvbGUuZ3JvdXBFbmQoKTtcblxuICAgICAgICAvLyBDaGFtYXIgY2FsbGJhY2sgZGUgY29uY2x1c8Ojb1xuICAgICAgICBvbkNvbXBsZXRlKGZ1bGxSZXNwb25zZSk7XG5cbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHJlYWRlci5yZWxlYXNlTG9jaygpO1xuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZ3JvdXAoJ+KdjCBBSSBTRVJWSUNFIC0gRVJSTyBDQVBUVVJBRE8nKTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ/CfmqggVGlwbyBkbyBlcnJvOicsIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5jb25zdHJ1Y3Rvci5uYW1lIDogdHlwZW9mIGVycm9yKTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ/Cfk50gTWVuc2FnZW06JywgZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBTdHJpbmcoZXJyb3IpKTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ/CflI0gU3RhY2sgdHJhY2U6JywgZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLnN0YWNrIDogJ04vQScpO1xuICAgICAgY29uc29sZS5lcnJvcign4o+wIFRpbWVzdGFtcDonLCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkpO1xuXG4gICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICBpZiAoZXJyb3IubmFtZSA9PT0gJ0Fib3J0RXJyb3InKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfm5EgUmVxdWlzacOnw6NvIGNhbmNlbGFkYSBwZWxvIHVzdcOhcmlvJyk7XG4gICAgICAgICAgY29uc29sZS5ncm91cEVuZCgpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zb2xlLmVycm9yKCfwn5SnIERldGFsaGVzIGNvbXBsZXRvczonLCBlcnJvcik7XG4gICAgICAgIGNvbnNvbGUuZ3JvdXBFbmQoKTtcbiAgICAgICAgb25FcnJvcihlcnJvci5tZXNzYWdlKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ/CflKcgRGV0YWxoZXMgY29tcGxldG9zOicsIGVycm9yKTtcbiAgICAgICAgY29uc29sZS5ncm91cEVuZCgpO1xuICAgICAgICBvbkVycm9yKCdFcnJvIGRlc2NvbmhlY2lkbyBuYSBjb211bmljYcOnw6NvIGNvbSBhIElBJyk7XG4gICAgICB9XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHRoaXMuYWJvcnRDb250cm9sbGVyID0gbnVsbDtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ2FuY2VsYSBhIHJlcXVpc2nDp8OjbyBlbSBhbmRhbWVudG9cbiAgICovXG4gIGNhbmNlbFJlcXVlc3QoKTogdm9pZCB7XG4gICAgaWYgKHRoaXMuYWJvcnRDb250cm9sbGVyKSB7XG4gICAgICB0aGlzLmFib3J0Q29udHJvbGxlci5hYm9ydCgpO1xuICAgICAgdGhpcy5hYm9ydENvbnRyb2xsZXIgPSBudWxsO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBWZXJpZmljYSBzZSBow6EgdW1hIHJlcXVpc2nDp8OjbyBlbSBhbmRhbWVudG9cbiAgICovXG4gIGlzUmVxdWVzdEluUHJvZ3Jlc3MoKTogYm9vbGVhbiB7XG4gICAgcmV0dXJuIHRoaXMuYWJvcnRDb250cm9sbGVyICE9PSBudWxsO1xuICB9XG5cbiAgLyoqXG4gICAqIENhcnJlZ2EgbWVuc2FnZW5zIGRlIHVtIGNoYXQgdXNhbmRvIGEgQVBJIHJvdXRlXG4gICAqL1xuICBhc3luYyBsb2FkQ2hhdE1lc3NhZ2VzKHVzZXJuYW1lOiBzdHJpbmcsIGNoYXRJZDogc3RyaW5nKTogUHJvbWlzZTxDaGF0TWVzc2FnZVtdPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvY2hhdC8ke3VzZXJuYW1lfS8ke2NoYXRJZH1gLCB7XG4gICAgICAgIG1ldGhvZDogJ0dFVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBFcnJvIGFvIGNhcnJlZ2FyIGNoYXQ6ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgY2hhdERhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICByZXR1cm4gY2hhdERhdGEubWVzc2FnZXMgfHwgW107XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBjYXJyZWdhciBtZW5zYWdlbnMgZG8gY2hhdDonLCBlcnJvcik7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIENvbnZlcnRlIG1lbnNhZ2VucyBkbyBmb3JtYXRvIGludGVybm8gcGFyYSBvIGZvcm1hdG8gZGEgSUFcbiAgICovXG4gIGNvbnZlcnRUb0FJRm9ybWF0KG1lc3NhZ2VzOiBhbnlbXSk6IENoYXRNZXNzYWdlW10ge1xuICAgIHJldHVybiBtZXNzYWdlcy5tYXAobXNnID0+ICh7XG4gICAgICBpZDogbXNnLmlkLFxuICAgICAgY29udGVudDogbXNnLmNvbnRlbnQsXG4gICAgICByb2xlOiBtc2cuc2VuZGVyID09PSAndXNlcicgPyAndXNlcicgOiAnYXNzaXN0YW50JyxcbiAgICAgIHRpbWVzdGFtcDogbXNnLnRpbWVzdGFtcCxcbiAgICAgIGlzRmF2b3JpdGU6IG1zZy5pc0Zhdm9yaXRlIHx8IGZhbHNlLFxuICAgIH0pKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBDb252ZXJ0ZSBtZW5zYWdlbnMgZG8gZm9ybWF0byBkYSBJQSBwYXJhIG8gZm9ybWF0byBpbnRlcm5vXG4gICAqL1xuICBjb252ZXJ0RnJvbUFJRm9ybWF0KG1lc3NhZ2VzOiBDaGF0TWVzc2FnZVtdKTogYW55W10ge1xuICAgIHJldHVybiBtZXNzYWdlcy5tYXAobXNnID0+ICh7XG4gICAgICBpZDogbXNnLmlkLFxuICAgICAgY29udGVudDogbXNnLmNvbnRlbnQsXG4gICAgICBzZW5kZXI6IG1zZy5yb2xlID09PSAndXNlcicgPyAndXNlcicgOiAnYWknLFxuICAgICAgdGltZXN0YW1wOiBtc2cudGltZXN0YW1wLFxuICAgICAgaXNGYXZvcml0ZTogbXNnLmlzRmF2b3JpdGUgfHwgZmFsc2UsXG4gICAgfSkpO1xuICB9XG5cbiAgLyoqXG4gICAqIEdlcmEgSUQgw7puaWNvIHBhcmEgbWVuc2FnZW5zXG4gICAqL1xuICBnZW5lcmF0ZU1lc3NhZ2VJZCgpOiBzdHJpbmcge1xuICAgIHJldHVybiBEYXRlLm5vdygpLnRvU3RyaW5nKDM2KSArIE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBWYWxpZGEgY29uZmlndXJhw6fDo28gYW50ZXMgZGUgZW52aWFyXG4gICAqL1xuICBwcml2YXRlIHZhbGlkYXRlQ29uZmlnKGNvbmZpZzogQUlTZXJ2aWNlQ29uZmlnKTogdm9pZCB7XG4gICAgaWYgKCFjb25maWcudXNlcm5hbWU/LnRyaW0oKSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdVc2VybmFtZSDDqSBvYnJpZ2F0w7NyaW8nKTtcbiAgICB9XG4gICAgaWYgKCFjb25maWcuY2hhdElkPy50cmltKCkpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignQ2hhdCBJRCDDqSBvYnJpZ2F0w7NyaW8nKTtcbiAgICB9XG4gICAgaWYgKCFjb25maWcubWVzc2FnZT8udHJpbSgpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ01lbnNhZ2VtIMOpIG9icmlnYXTDs3JpYScpO1xuICAgIH1cbiAgICBpZiAoY29uZmlnLm1lc3NhZ2UubGVuZ3RoID4gMTAwMDApIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTWVuc2FnZW0gbXVpdG8gbG9uZ2EgKG3DoXhpbW8gMTAuMDAwIGNhcmFjdGVyZXMpJyk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEVudmlhIG1lbnNhZ2VtIGNvbSB2YWxpZGHDp8Ojb1xuICAgKi9cbiAgYXN5bmMgc2VuZE1lc3NhZ2VTYWZlKFxuICAgIGNvbmZpZzogQUlTZXJ2aWNlQ29uZmlnLFxuICAgIG9uQ2h1bms6IChjaHVuazogc3RyaW5nKSA9PiB2b2lkLFxuICAgIG9uQ29tcGxldGU6IChmdWxsUmVzcG9uc2U6IHN0cmluZykgPT4gdm9pZCxcbiAgICBvbkVycm9yOiAoZXJyb3I6IHN0cmluZykgPT4gdm9pZFxuICApOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBjb25zb2xlLmdyb3VwKCfwn5qAIEFJIFNFUlZJQ0UgLSBTRU5EIE1FU1NBR0UgU0FGRScpO1xuICAgIGNvbnNvbGUubG9nKCfwn5SnIENvbmZpZyByZWNlYmlkYTonLCB7XG4gICAgICB1c2VybmFtZTogY29uZmlnLnVzZXJuYW1lLFxuICAgICAgY2hhdElkOiBjb25maWcuY2hhdElkLFxuICAgICAgbWVzc2FnZUxlbmd0aDogY29uZmlnLm1lc3NhZ2U/Lmxlbmd0aCB8fCAwLFxuICAgICAgbW9kZWw6IGNvbmZpZy5tb2RlbCxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgfSk7XG4gICAgY29uc29sZS5sb2coJ/Cfk4sgQ2FsbGJhY2tzOicsIHtcbiAgICAgIGhhc09uQ2h1bms6IHR5cGVvZiBvbkNodW5rID09PSAnZnVuY3Rpb24nLFxuICAgICAgaGFzT25Db21wbGV0ZTogdHlwZW9mIG9uQ29tcGxldGUgPT09ICdmdW5jdGlvbicsXG4gICAgICBoYXNPbkVycm9yOiB0eXBlb2Ygb25FcnJvciA9PT0gJ2Z1bmN0aW9uJ1xuICAgIH0pO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgVmFsaWRhbmRvIGNvbmZpZ3VyYcOnw6NvLi4uJyk7XG4gICAgICB0aGlzLnZhbGlkYXRlQ29uZmlnKGNvbmZpZyk7XG4gICAgICBjb25zb2xlLmxvZygn4pyFIENvbmZpZ3VyYcOnw6NvIHbDoWxpZGEsIGVudmlhbmRvIG1lbnNhZ2VtLi4uJyk7XG4gICAgICBhd2FpdCB0aGlzLnNlbmRNZXNzYWdlKGNvbmZpZywgb25DaHVuaywgb25Db21wbGV0ZSwgb25FcnJvcik7XG4gICAgICBjb25zb2xlLmxvZygn4pyFIE1lbnNhZ2VtIGVudmlhZGEgY29tIHN1Y2Vzc28nKTtcbiAgICAgIGNvbnNvbGUuZ3JvdXBFbmQoKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5ncm91cCgn4p2MIEVSUk8gRU0gU0VORCBNRVNTQUdFIFNBRkUnKTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ/CfmqggRXJybyBjYXB0dXJhZG86JywgZXJyb3IpO1xuICAgICAgY29uc29sZS5lcnJvcign8J+TnSBUaXBvOicsIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5jb25zdHJ1Y3Rvci5uYW1lIDogdHlwZW9mIGVycm9yKTtcbiAgICAgIGNvbnNvbGUuZ3JvdXBFbmQoKTtcblxuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IpIHtcbiAgICAgICAgb25FcnJvcihlcnJvci5tZXNzYWdlKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG9uRXJyb3IoJ0Vycm8gZGUgdmFsaWRhw6fDo28nKTtcbiAgICAgIH1cbiAgICAgIGNvbnNvbGUuZ3JvdXBFbmQoKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogRGVsZXRhIHVtYSBtZW5zYWdlbSBkbyBjaGF0IG5vIEZpcmViYXNlIFN0b3JhZ2VcbiAgICovXG4gIGFzeW5jIGRlbGV0ZU1lc3NhZ2UodXNlcm5hbWU6IHN0cmluZywgY2hhdElkOiBzdHJpbmcsIG1lc3NhZ2VJZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvY2hhdC8ke3VzZXJuYW1lfS8ke2NoYXRJZH0vbWVzc2FnZS8ke21lc3NhZ2VJZH1gLCB7XG4gICAgICAgIG1ldGhvZDogJ0RFTEVURScsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBFcnJvIGFvIGRlbGV0YXIgbWVuc2FnZW06ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gZGVsZXRhciBtZW5zYWdlbTonLCBlcnJvcik7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEF0dWFsaXphIHVtYSBtZW5zYWdlbSBubyBjaGF0IG5vIEZpcmViYXNlIFN0b3JhZ2VcbiAgICovXG4gIGFzeW5jIHVwZGF0ZU1lc3NhZ2UodXNlcm5hbWU6IHN0cmluZywgY2hhdElkOiBzdHJpbmcsIG1lc3NhZ2VJZDogc3RyaW5nLCBuZXdDb250ZW50OiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9jaGF0LyR7dXNlcm5hbWV9LyR7Y2hhdElkfS9tZXNzYWdlLyR7bWVzc2FnZUlkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgY29udGVudDogbmV3Q29udGVudCB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRXJybyBhbyBhdHVhbGl6YXIgbWVuc2FnZW06ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gYXR1YWxpemFyIG1lbnNhZ2VtOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cbn1cblxuLy8gRXhwb3J0YXIgaW5zdMOibmNpYSBzaW5nbGV0b25cbmV4cG9ydCBjb25zdCBhaVNlcnZpY2UgPSBuZXcgQUlTZXJ2aWNlKCk7XG5leHBvcnQgZGVmYXVsdCBhaVNlcnZpY2U7XG4iXSwibmFtZXMiOlsiQUlTZXJ2aWNlIiwic2VuZE1lc3NhZ2UiLCJjb25maWciLCJvbkNodW5rIiwib25Db21wbGV0ZSIsIm9uRXJyb3IiLCJyZXF1ZXN0RGF0YSIsImFib3J0Q29udHJvbGxlciIsIkFib3J0Q29udHJvbGxlciIsInVzZXJuYW1lIiwiY2hhdElkIiwibWVzc2FnZSIsIm1vZGVsIiwiY29uc29sZSIsImdyb3VwIiwibG9nIiwiZnVuY3Rpb25VcmwiLCJtZXNzYWdlTGVuZ3RoIiwibGVuZ3RoIiwibWVzc2FnZVByZXZpZXciLCJzdWJzdHJpbmciLCJ0aW1lc3RhbXAiLCJEYXRlIiwidG9JU09TdHJpbmciLCJKU09OIiwic3RyaW5naWZ5IiwiYm9keVNpemUiLCJoYXNBYm9ydENvbnRyb2xsZXIiLCJ1c2VyQWdlbnQiLCJuYXZpZ2F0b3IiLCJkb2MiLCJnZXREb2MiLCJkYiIsImNoYXREb2NSZWYiLCJjaGF0RG9jU25hcCIsImV4aXN0cyIsImNoYXRDb25maWciLCJkYXRhIiwic3lzdGVtUHJvbXB0IiwiY29udGVudCIsInByZXZpZXciLCJjb250ZXh0IiwidGVtcGVyYXR1cmUiLCJ0ZW1wZXJhdHVyZVR5cGUiLCJmcmVxdWVuY3lQZW5hbHR5IiwiZnJlcXVlbmN5UGVuYWx0eVR5cGUiLCJyZXBldGl0aW9uUGVuYWx0eSIsInJlcGV0aXRpb25QZW5hbHR5VHlwZSIsIm1heFRva2VucyIsIm1heFRva2Vuc1R5cGUiLCJsYXRleEluc3RydWN0aW9ucyIsImxhc3RVc2VkTW9kZWwiLCJjcmVhdGVkQXQiLCJsYXN0VXBkYXRlZEF0IiwibmFtZSIsImdyb3VwRW5kIiwid2FybiIsImVycm9yIiwibWVzc2FnZXNSZXNwb25zZSIsImZldGNoIiwib2siLCJjaGF0RGF0YSIsImpzb24iLCJtZXNzYWdlcyIsInNsaWNlIiwibWFwIiwibXNnIiwiaW5kZXgiLCJpZCIsInJvbGUiLCJjb250ZW50TGVuZ3RoIiwiY29udGVudFByZXZpZXciLCJoYXNBdHRhY2htZW50cyIsImF0dGFjaG1lbnRzIiwiYXR0YWNobWVudENvdW50IiwidXNlck1lc3NhZ2VzIiwiZmlsdGVyIiwiYXNzaXN0YW50TWVzc2FnZXMiLCJ0b3RhbENoYXJzIiwicmVkdWNlIiwiYWNjIiwidG90YWxNZXNzYWdlcyIsInRvdGFsQ2hhcmFjdGVycyIsImF2ZXJhZ2VNZXNzYWdlTGVuZ3RoIiwiTWF0aCIsInJvdW5kIiwib2xkZXN0TWVzc2FnZSIsIm5ld2VzdE1lc3NhZ2UiLCJzdGF0dXMiLCJyZXNwb25zZSIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5Iiwic2lnbmFsIiwic3RhdHVzVGV4dCIsIk9iamVjdCIsImZyb21FbnRyaWVzIiwiZW50cmllcyIsInVybCIsImVycm9yRGF0YSIsIkVycm9yIiwicmVhZGVyIiwiZ2V0UmVhZGVyIiwiZGVjb2RlciIsIlRleHREZWNvZGVyIiwiZnVsbFJlc3BvbnNlIiwiY2h1bmtDb3VudCIsInRvdGFsQnl0ZXMiLCJkb25lIiwidmFsdWUiLCJyZWFkIiwidG90YWxDaHVua3MiLCJyZXNwb25zZUxlbmd0aCIsImF2Z0NodW5rU2l6ZSIsInJhd0J5dGVzIiwiQXJyYXkiLCJmcm9tIiwiYiIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJqb2luIiwiYnl0ZUxlbmd0aCIsImZpcnN0Qnl0ZXMiLCJsYXN0Qnl0ZXMiLCJjaHVuayIsImRlY29kZSIsInN0cmVhbSIsIm9yaWdpbmFsQ2h1bmsiLCJyZXBsYWNlIiwib3JpZ2luYWwiLCJjbGVhbmVkIiwicmVtb3ZlZENoYXJzIiwiY2hhciIsImkiLCJjIiwiY2hhckNvZGVBdCIsInNpemUiLCJjaGFyQ29kZXMiLCJoYXNOb25Bc2NpaSIsInRlc3QiLCJoYXNDb250cm9sQ2hhcnMiLCJzdXNwaWNpb3VzUGF0dGVybnMiLCJoYXNTdXNwaWNpb3VzQ29udGVudCIsInNvbWUiLCJwYXR0ZXJuIiwid29yZENvdW50Iiwic3BsaXQiLCJyZWxlYXNlTG9jayIsImNvbnN0cnVjdG9yIiwiU3RyaW5nIiwic3RhY2siLCJjYW5jZWxSZXF1ZXN0IiwiYWJvcnQiLCJpc1JlcXVlc3RJblByb2dyZXNzIiwibG9hZENoYXRNZXNzYWdlcyIsImNvbnZlcnRUb0FJRm9ybWF0Iiwic2VuZGVyIiwiaXNGYXZvcml0ZSIsImNvbnZlcnRGcm9tQUlGb3JtYXQiLCJnZW5lcmF0ZU1lc3NhZ2VJZCIsIm5vdyIsInJhbmRvbSIsInZhbGlkYXRlQ29uZmlnIiwidHJpbSIsInNlbmRNZXNzYWdlU2FmZSIsImhhc09uQ2h1bmsiLCJoYXNPbkNvbXBsZXRlIiwiaGFzT25FcnJvciIsImRlbGV0ZU1lc3NhZ2UiLCJtZXNzYWdlSWQiLCJ1cGRhdGVNZXNzYWdlIiwibmV3Q29udGVudCIsImFpU2VydmljZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});