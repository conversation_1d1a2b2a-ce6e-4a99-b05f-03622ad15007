"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n// Serviço para integração com Firebase Functions de IA\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            var _requestData_message, _requestData_message1, _requestData_message2;\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model\n            };\n            // DEBUG: Log detalhado no console do navegador\n            console.group(\"\\uD83E\\uDD16 AI SERVICE - ENVIANDO REQUISI\\xc7\\xc3O\");\n            console.log(\"\\uD83D\\uDCE1 URL da Function:\", this.functionUrl);\n            console.log(\"\\uD83D\\uDCCB Dados da Requisi\\xe7\\xe3o:\", {\n                username: requestData.username,\n                chatId: requestData.chatId,\n                message: requestData.message,\n                messageLength: ((_requestData_message = requestData.message) === null || _requestData_message === void 0 ? void 0 : _requestData_message.length) || 0,\n                messagePreview: ((_requestData_message1 = requestData.message) === null || _requestData_message1 === void 0 ? void 0 : _requestData_message1.substring(0, 100)) + (((_requestData_message2 = requestData.message) === null || _requestData_message2 === void 0 ? void 0 : _requestData_message2.length) > 100 ? \"...\" : \"\"),\n                model: requestData.model,\n                timestamp: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDD27 Headers:\", {\n                \"Content-Type\": \"application/json\",\n                \"Method\": \"POST\"\n            });\n            console.log(\"\\uD83D\\uDCE6 Body JSON:\", JSON.stringify(requestData, null, 2));\n            console.log(\"\\uD83D\\uDCCA Estat\\xedsticas:\", {\n                bodySize: JSON.stringify(requestData).length + \" bytes\",\n                hasAbortController: !!this.abortController,\n                userAgent: navigator.userAgent\n            });\n            // Buscar configurações do chat do Firestore para mostrar nos logs\n            console.log(\"\\uD83D\\uDD0D Buscando configura\\xe7\\xf5es do chat do Firestore para debug...\");\n            try {\n                // Importar Firebase dinamicamente para evitar problemas de SSR\n                const { doc, getDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                const { db } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\"));\n                const chatDocRef = doc(db, \"usuarios\", requestData.username, \"conversas\", requestData.chatId);\n                const chatDocSnap = await getDoc(chatDocRef);\n                if (chatDocSnap.exists()) {\n                    var _chatConfig_systemPrompt, _chatConfig_systemPrompt1, _chatConfig_systemPrompt2, _chatConfig_context, _chatConfig_context1, _chatConfig_context2;\n                    const chatConfig = chatDocSnap.data();\n                    console.group(\"⚙️ CONFIGURA\\xc7\\xd5ES DO CHAT (FIRESTORE)\");\n                    console.log(\"\\uD83C\\uDFAF System Prompt:\", {\n                        length: ((_chatConfig_systemPrompt = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt === void 0 ? void 0 : _chatConfig_systemPrompt.length) || 0,\n                        content: chatConfig.systemPrompt || \"(vazio)\",\n                        preview: ((_chatConfig_systemPrompt1 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt1 === void 0 ? void 0 : _chatConfig_systemPrompt1.substring(0, 200)) + (((_chatConfig_systemPrompt2 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt2 === void 0 ? void 0 : _chatConfig_systemPrompt2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83D\\uDCDD Contexto:\", {\n                        length: ((_chatConfig_context = chatConfig.context) === null || _chatConfig_context === void 0 ? void 0 : _chatConfig_context.length) || 0,\n                        content: chatConfig.context || \"(vazio)\",\n                        preview: ((_chatConfig_context1 = chatConfig.context) === null || _chatConfig_context1 === void 0 ? void 0 : _chatConfig_context1.substring(0, 200)) + (((_chatConfig_context2 = chatConfig.context) === null || _chatConfig_context2 === void 0 ? void 0 : _chatConfig_context2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83C\\uDF21️ Par\\xe2metros de Gera\\xe7\\xe3o:\", {\n                        temperature: chatConfig.temperature,\n                        temperatureType: typeof chatConfig.temperature,\n                        frequencyPenalty: chatConfig.frequencyPenalty,\n                        frequencyPenaltyType: typeof chatConfig.frequencyPenalty,\n                        repetitionPenalty: chatConfig.repetitionPenalty,\n                        repetitionPenaltyType: typeof chatConfig.repetitionPenalty,\n                        maxTokens: chatConfig.maxTokens,\n                        maxTokensType: typeof chatConfig.maxTokens,\n                        latexInstructions: chatConfig.latexInstructions\n                    });\n                    console.log(\"\\uD83D\\uDCCA Metadados:\", {\n                        lastUsedModel: chatConfig.lastUsedModel,\n                        createdAt: chatConfig.createdAt,\n                        lastUpdatedAt: chatConfig.lastUpdatedAt,\n                        name: chatConfig.name\n                    });\n                    console.groupEnd();\n                } else {\n                    console.warn(\"⚠️ Documento do chat n\\xe3o encontrado no Firestore\");\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar configura\\xe7\\xf5es do chat do Firestore:\", error);\n            }\n            // Buscar histórico de mensagens do Storage para mostrar nos logs\n            console.log(\"\\uD83D\\uDD0D Buscando hist\\xf3rico de mensagens do Storage para debug...\");\n            try {\n                const messagesResponse = await fetch(\"/api/chat/\".concat(requestData.username, \"/\").concat(requestData.chatId));\n                if (messagesResponse.ok) {\n                    const chatData = await messagesResponse.json();\n                    const messages = chatData.messages || [];\n                    console.group(\"\\uD83D\\uDCAC HIST\\xd3RICO DE MENSAGENS (STORAGE)\");\n                    console.log(\"\\uD83D\\uDCCA Total de mensagens:\", messages.length);\n                    if (messages.length > 0) {\n                        var _messages_, _messages_1;\n                        console.log(\"\\uD83D\\uDCCB \\xdaltimas 5 mensagens:\", messages.slice(-5).map((msg, index)=>{\n                            var _msg_content, _msg_content1, _msg_content2, _msg_attachments;\n                            return {\n                                index: messages.length - 5 + index,\n                                id: msg.id,\n                                role: msg.role,\n                                contentLength: ((_msg_content = msg.content) === null || _msg_content === void 0 ? void 0 : _msg_content.length) || 0,\n                                contentPreview: ((_msg_content1 = msg.content) === null || _msg_content1 === void 0 ? void 0 : _msg_content1.substring(0, 100)) + (((_msg_content2 = msg.content) === null || _msg_content2 === void 0 ? void 0 : _msg_content2.length) > 100 ? \"...\" : \"\"),\n                                timestamp: msg.timestamp,\n                                hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                                attachmentCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0\n                            };\n                        }));\n                        if (messages.length > 5) {\n                            console.log(\"\\uD83D\\uDCDC Mensagens mais antigas:\", \"\".concat(messages.length - 5, \" mensagens anteriores n\\xe3o mostradas\"));\n                        }\n                        // Estatísticas do histórico\n                        const userMessages = messages.filter((msg)=>msg.role === \"user\").length;\n                        const assistantMessages = messages.filter((msg)=>msg.role === \"assistant\").length;\n                        const totalChars = messages.reduce((acc, msg)=>{\n                            var _msg_content;\n                            return acc + (((_msg_content = msg.content) === null || _msg_content === void 0 ? void 0 : _msg_content.length) || 0);\n                        }, 0);\n                        console.log(\"\\uD83D\\uDCC8 Estat\\xedsticas do Hist\\xf3rico:\", {\n                            totalMessages: messages.length,\n                            userMessages,\n                            assistantMessages,\n                            totalCharacters: totalChars,\n                            averageMessageLength: messages.length > 0 ? Math.round(totalChars / messages.length) : 0,\n                            oldestMessage: (_messages_ = messages[0]) === null || _messages_ === void 0 ? void 0 : _messages_.timestamp,\n                            newestMessage: (_messages_1 = messages[messages.length - 1]) === null || _messages_1 === void 0 ? void 0 : _messages_1.timestamp\n                        });\n                    } else {\n                        console.log(\"\\uD83D\\uDCED Nenhuma mensagem encontrada no hist\\xf3rico\");\n                    }\n                    console.groupEnd();\n                } else {\n                    console.warn(\"⚠️ Falha ao buscar mensagens - Status:\", messagesResponse.status);\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar hist\\xf3rico de mensagens:\", error);\n            }\n            console.groupEnd();\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            // DEBUG: Log da resposta\n            console.group(\"\\uD83D\\uDCE1 AI SERVICE - RESPOSTA RECEBIDA\");\n            console.log(\"✅ Status:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCCB Headers:\", Object.fromEntries(response.headers.entries()));\n            console.log(\"\\uD83D\\uDD0D Response OK:\", response.ok);\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            // Processar stream\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        console.log(\"✅ Stream finalizado\");\n                        console.log(\"\\uD83D\\uDCCA Estat\\xedsticas finais:\", {\n                            totalChunks: chunkCount,\n                            totalBytes: totalBytes,\n                            responseLength: fullResponse.length,\n                            avgChunkSize: chunkCount > 0 ? Math.round(totalBytes / chunkCount) : 0\n                        });\n                        console.groupEnd();\n                        break;\n                    }\n                    chunkCount++;\n                    totalBytes += (value === null || value === void 0 ? void 0 : value.length) || 0;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                console.group(\"✅ AI SERVICE - RESPOSTA COMPLETA\");\n                console.log(\"\\uD83D\\uDCDD Resposta final:\", {\n                    length: fullResponse.length,\n                    preview: fullResponse.substring(0, 200) + (fullResponse.length > 200 ? \"...\" : \"\"),\n                    wordCount: fullResponse.split(\" \").length,\n                    timestamp: new Date().toISOString()\n                });\n                console.groupEnd();\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            console.group(\"❌ AI SERVICE - ERRO CAPTURADO\");\n            console.error(\"\\uD83D\\uDEA8 Tipo do erro:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.error(\"\\uD83D\\uDCDD Mensagem:\", error instanceof Error ? error.message : String(error));\n            console.error(\"\\uD83D\\uDD0D Stack trace:\", error instanceof Error ? error.stack : \"N/A\");\n            console.error(\"⏰ Timestamp:\", new Date().toISOString());\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    console.log(\"\\uD83D\\uDED1 Requisi\\xe7\\xe3o cancelada pelo usu\\xe1rio\");\n                    console.groupEnd();\n                    return;\n                }\n                console.error(\"\\uD83D\\uDD27 Detalhes completos:\", error);\n                console.groupEnd();\n                onError(error.message);\n            } else {\n                console.error(\"\\uD83D\\uDD27 Detalhes completos:\", error);\n                console.groupEnd();\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        var _config_message;\n        console.group(\"\\uD83D\\uDE80 AI SERVICE - SEND MESSAGE SAFE\");\n        console.log(\"\\uD83D\\uDD27 Config recebida:\", {\n            username: config.username,\n            chatId: config.chatId,\n            messageLength: ((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.length) || 0,\n            model: config.model,\n            timestamp: new Date().toISOString()\n        });\n        console.log(\"\\uD83D\\uDCCB Callbacks:\", {\n            hasOnChunk: typeof onChunk === \"function\",\n            hasOnComplete: typeof onComplete === \"function\",\n            hasOnError: typeof onError === \"function\"\n        });\n        try {\n            console.log(\"✅ Validando configura\\xe7\\xe3o...\");\n            this.validateConfig(config);\n            console.log(\"✅ Configura\\xe7\\xe3o v\\xe1lida, enviando mensagem...\");\n            await this.sendMessage(config, onChunk, onComplete, onError);\n            console.log(\"✅ Mensagem enviada com sucesso\");\n            console.groupEnd();\n        } catch (error) {\n            console.group(\"❌ ERRO EM SEND MESSAGE SAFE\");\n            console.error(\"\\uD83D\\uDEA8 Erro capturado:\", error);\n            console.error(\"\\uD83D\\uDCDD Tipo:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.groupEnd();\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n            console.groupEnd();\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});