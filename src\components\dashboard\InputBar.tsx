'use client';

import { useState, useRef, useEffect } from 'react';
import { Send, Paperclip, Mic, Square, ArrowUp, ArrowDown, Bot, Zap, Settings } from 'lucide-react';

interface Attachment {
  id: string;
  filename: string;
  type: 'image' | 'document';
  file: File;
}

interface InputBarProps {
  message: string;
  setMessage: (message: string) => void;
  onSendMessage: () => void;
  isLoading: boolean;
  selectedModel: string;
  onModelChange: (model: string) => void;
  onScrollToTop?: () => void;
  onScrollToBottom?: () => void;
  isStreaming?: boolean;
  onCancelStreaming?: () => void;
  onOpenModelModal?: () => void;
}

const AI_MODELS = [
  { id: 'gpt-4.1-nano', name: 'GPT-4.1 Nano', description: 'Rápido e eficiente' },
  { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: '<PERSON><PERSON> poderoso' },
  { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', description: 'Criativo e preciso' },
  { id: 'gemini-pro', name: '<PERSON> Pro', description: 'Multimodal' },
];

export default function InputBar({
  message,
  setMessage,
  onSendMessage,
  isLoading,
  selectedModel,
  onModelChange,
  onScrollToTop,
  onScrollToBottom,
  isStreaming = false,
  onCancelStreaming,
  onOpenModelModal
}: InputBarProps) {

  const [webSearchEnabled, setWebSearchEnabled] = useState(false);
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const adjustHeightTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (adjustHeightTimeoutRef.current) {
        clearTimeout(adjustHeightTimeoutRef.current);
      }
    };
  }, []);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);

    // Debounce o ajuste de altura para melhor performance
    if (adjustHeightTimeoutRef.current) {
      clearTimeout(adjustHeightTimeoutRef.current);
    }

    adjustHeightTimeoutRef.current = setTimeout(() => {
      adjustTextareaHeight();
    }, 16); // ~60fps
  };

  const handleSend = () => {
    if ((message?.trim() || attachments.length > 0) && !isLoading && !isUploading) {
      onSendMessage();
    }
  };

  const handleAttachment = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;

    setIsUploading(true);
    try {
      for (const file of Array.from(files)) {
        const attachment: Attachment = {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          filename: file.name,
          type: file.type.startsWith('image/') ? 'image' : 'document',
          file
        };
        setAttachments(prev => [...prev, attachment]);
      }
    } catch (error) {
      console.error('Erro ao processar arquivos:', error);
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const removeAttachment = (id: string) => {
    setAttachments(prev => prev.filter(att => att.id !== id));
  };

  const handleWebSearch = () => {
    setWebSearchEnabled(!webSearchEnabled);
  };

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Otimizar para evitar múltiplos reflows
      const currentHeight = textarea.style.height;
      textarea.style.height = 'auto';
      const newHeight = Math.min(textarea.scrollHeight, 120) + 'px';

      // Só atualizar se a altura realmente mudou
      if (currentHeight !== newHeight) {
        textarea.style.height = newHeight;
      }
    }
  };

  const isWebSearchEnabled = () => {
    return selectedModel.includes('openrouter') || selectedModel.includes('gpt-4');
  };

  const currentModel = AI_MODELS.find(model => model.id === selectedModel);
  const modelName = currentModel ? currentModel.name : selectedModel;
  const selectedAttachmentsCount = attachments.length;

  return (
    <div className="p-3 sm:p-4 lg:p-6 border-t border-blue-700/30 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl relative">
      {/* Efeito de brilho sutil */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none"></div>
      
      <div className="max-w-4xl mx-auto relative z-10">
        {/* Attachment Preview */}
        {attachments.length > 0 && (
          <div className="mb-3 sm:mb-4 p-3 sm:p-4 bg-blue-900/30 backdrop-blur-sm rounded-lg sm:rounded-xl border border-blue-600/30">
            <div className="flex flex-wrap gap-2 sm:gap-3">
              {attachments.map((attachment) => (
                <div
                  key={attachment.id}
                  className="flex items-center space-x-2 sm:space-x-3 bg-blue-800/40 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 border border-blue-600/20"
                >
                  {attachment.type === 'image' ? (
                    <svg className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  ) : (
                    <svg className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-red-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  )}
                  <span className="text-xs sm:text-sm text-blue-100 truncate max-w-20 sm:max-w-32 font-medium">
                    {attachment.filename}
                  </span>
                  <button
                    onClick={() => removeAttachment(attachment.id)}
                    className="text-blue-300 hover:text-red-400 transition-colors p-0.5 sm:p-1 rounded-full hover:bg-red-500/20 flex-shrink-0"
                  >
                    <svg className="w-3.5 h-3.5 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex items-end space-x-2 sm:space-x-3">
          {/* Floating input container */}
          <div className="flex-1 relative bg-gradient-to-r from-blue-900/80 to-blue-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-blue-600/30 shadow-xl shadow-blue-900/50">
            {/* Neumorphic glow effect */}
            <div className="absolute inset-0 rounded-xl sm:rounded-2xl shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]"></div>

            <div className="relative z-10 flex items-end space-x-2 sm:space-x-3 p-3 sm:p-4">
              {/* Left side buttons */}
              <div className="flex items-center space-x-1 sm:space-x-2">
                {/* Model Selection Button */}
                <button
                  onClick={onOpenModelModal}
                  className="p-2 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 hover:scale-105"
                  title="Selecionar modelo"
                >
                  <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </button>

                {/* Attachment Button */}
                <button
                  onClick={handleAttachment}
                  disabled={isUploading}
                  className="p-2.5 rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105"
                  title="Anexar arquivo"
                >
                  {isUploading ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-400"></div>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                    </svg>
                  )}
                </button>

                {/* Web Search Button - Only show for OpenRouter models */}
                {isWebSearchEnabled() && (
                  <button
                    onClick={handleWebSearch}
                    className={`p-2.5 rounded-xl transition-all duration-200 hover:shadow-lg border hover:scale-105 backdrop-blur-sm ${
                      webSearchEnabled
                        ? 'bg-cyan-600/30 hover:bg-cyan-600/40 text-cyan-200 hover:text-cyan-100 border-cyan-500/50 shadow-lg shadow-cyan-500/20'
                        : 'bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 border-blue-600/20 hover:shadow-blue-500/20'
                    }`}
                    title={`Busca na web (OpenRouter) - ${webSearchEnabled ? 'Ativada' : 'Desativada'}`}
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z" />
                    </svg>
                  </button>
                )}
              </div>

              {/* Text Input */}
              <div className="flex-1 relative">
                <textarea
                  ref={textareaRef}
                  value={message}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyPress}
                  className="w-full bg-transparent border-none rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none resize-none text-sm leading-relaxed min-h-[44px] max-h-[120px] selection:bg-blue-500/30"
                  rows={1}
                  placeholder="Digite sua mensagem aqui... ✨"
                  disabled={isLoading || isStreaming}
                />
              </div>

              {/* Send/Cancel Button */}
              {isStreaming ? (
                <button
                  onClick={onCancelStreaming}
                  className="p-3 rounded-xl bg-gradient-to-r from-red-600 via-red-500 to-red-600 hover:from-red-500 hover:via-red-400 hover:to-red-500 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-red-500/30"
                  title="Parar geração"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              ) : (
                <button
                  onClick={handleSend}
                  disabled={(!message?.trim() && attachments.length === 0) || isLoading || isUploading || isStreaming}
                  className="p-3 rounded-xl bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105 disabled:hover:scale-100 border border-blue-500/30"
                  title="Enviar mensagem"
                >
                  {isLoading ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white"></div>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  )}
                </button>
              )}
            </div>

            {/* Selected Model and Attachments Indicators */}
            <div className="absolute -top-8 left-4 flex items-center space-x-2">
              {selectedModel && (
                <div className="bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg">
                  <span className="text-xs text-blue-300">Modelo: </span>
                  <span className="text-xs text-cyan-300 font-medium">{modelName}</span>
                </div>
              )}

              {selectedAttachmentsCount > 0 && (
                <div className="bg-green-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-green-600/30 shadow-lg">
                  <span className="text-xs text-green-300">Anexos: </span>
                  <span className="text-xs text-green-200 font-medium">{selectedAttachmentsCount}</span>
                </div>
              )}
            </div>
          </div>

          {/* Scroll Buttons - Side by side next to input */}
          <div className="flex flex-col space-y-2">
            {/* Scroll to Top Button */}
            {onScrollToTop && (
              <button
                onClick={onScrollToTop}
                className="group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95"
                title="Ir para o topo"
              >
                {/* Glow effect */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
                <svg className="relative w-5 h-5 transform group-hover:-translate-y-0.5 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
              </button>
            )}

            {/* Scroll to Bottom Button */}
            {onScrollToBottom && (
              <button
                onClick={onScrollToBottom}
                className="group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95"
                title="Ir para o final"
              >
                {/* Glow effect */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
                <svg className="relative w-5 h-5 transform group-hover:translate-y-0.5 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M19 14l-7 7m0 0l-7-7m7 7V4" />
                </svg>
              </button>
            )}
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/png,image/jpeg,image/webp,application/pdf"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>
    </div>
  );
}
