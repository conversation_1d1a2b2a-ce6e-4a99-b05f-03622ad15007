"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/MarkdownRenderer.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remark-math */ \"(app-pages-browser)/./node_modules/remark-math/lib/index.js\");\n/* harmony import */ var rehype_katex__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rehype-katex */ \"(app-pages-browser)/./node_modules/rehype-katex/lib/index.js\");\n/* harmony import */ var rehype_highlight__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rehype-highlight */ \"(app-pages-browser)/./node_modules/rehype-highlight/lib/index.js\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _styles_markdown_latex_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/markdown-latex.css */ \"(app-pages-browser)/./src/styles/markdown-latex.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Importar estilos do KaTeX\n\n\nconst MarkdownRenderer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c = (param)=>{\n    let { content, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"markdown-content \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                remark_math__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n            ],\n            rehypePlugins: [\n                rehype_katex__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                [\n                    rehype_highlight__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    {\n                        detect: true,\n                        ignoreMissing: true\n                    }\n                ]\n            ],\n            components: {\n                // Customizar renderização de código\n                code (param) {\n                    let { node, inline, className, children, ...props } = param;\n                    const match = /language-(\\w+)/.exec(className || \"\");\n                    return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"bg-gray-800 rounded-lg p-4 overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: className,\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0)\n                    }, void 0, false, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-700 px-1.5 py-0.5 rounded text-sm font-mono\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de links\n                a (param) {\n                    let { children, href, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: href,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"text-blue-400 hover:text-blue-300 underline\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de tabelas\n                table (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto my-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full border-collapse border border-gray-600\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0)\n                    }, void 0, false, void 0, void 0);\n                },\n                th (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                        className: \"border border-gray-600 bg-gray-700 px-4 py-2 text-left font-semibold\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                td (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"border border-gray-600 px-4 py-2\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de blockquotes\n                blockquote (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-blue-500 pl-4 py-2 my-4 bg-gray-800/50 italic\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de listas\n                ul (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside space-y-1 my-2\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                ol (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside space-y-1 my-2\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de títulos\n                h1 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-4 mt-6 text-white\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                h2 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-3 mt-5 text-white\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                h3 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold mb-2 mt-4 text-white\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de parágrafos\n                p (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-3 leading-relaxed text-gray-200\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de linha horizontal\n                hr (param) {\n                    let { ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"border-gray-600 my-6\",\n                        ...props\n                    }, void 0, false, void 0, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = MarkdownRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MarkdownRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MarkdownRenderer$React.memo\");\n$RefreshReg$(_c1, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx\n"));

/***/ })

});