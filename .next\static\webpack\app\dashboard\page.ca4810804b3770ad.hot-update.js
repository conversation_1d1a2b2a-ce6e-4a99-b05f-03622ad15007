"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n// Serviço para integração com Firebase Functions de IA\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            var _requestData_message, _requestData_message1, _requestData_message2;\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model\n            };\n            // DEBUG: Log detalhado no console do navegador\n            console.group(\"\\uD83E\\uDD16 AI SERVICE - ENVIANDO REQUISI\\xc7\\xc3O\");\n            console.log(\"\\uD83D\\uDCE1 URL da Function:\", this.functionUrl);\n            console.log(\"\\uD83D\\uDCCB Dados da Requisi\\xe7\\xe3o:\", {\n                username: requestData.username,\n                chatId: requestData.chatId,\n                message: requestData.message,\n                messageLength: ((_requestData_message = requestData.message) === null || _requestData_message === void 0 ? void 0 : _requestData_message.length) || 0,\n                messagePreview: ((_requestData_message1 = requestData.message) === null || _requestData_message1 === void 0 ? void 0 : _requestData_message1.substring(0, 100)) + (((_requestData_message2 = requestData.message) === null || _requestData_message2 === void 0 ? void 0 : _requestData_message2.length) > 100 ? \"...\" : \"\"),\n                model: requestData.model,\n                timestamp: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDD27 Headers:\", {\n                \"Content-Type\": \"application/json\",\n                \"Method\": \"POST\"\n            });\n            console.log(\"\\uD83D\\uDCE6 Body JSON:\", JSON.stringify(requestData, null, 2));\n            console.log(\"\\uD83D\\uDCCA Estat\\xedsticas:\", {\n                bodySize: JSON.stringify(requestData).length + \" bytes\",\n                hasAbortController: !!this.abortController,\n                userAgent: navigator.userAgent\n            });\n            // Buscar configurações do chat do Firestore para mostrar nos logs\n            console.log(\"\\uD83D\\uDD0D Buscando configura\\xe7\\xf5es do chat do Firestore para debug...\");\n            try {\n                // Importar Firebase dinamicamente para evitar problemas de SSR\n                const { doc, getDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                const { db } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\"));\n                const chatDocRef = doc(db, \"usuarios\", requestData.username, \"conversas\", requestData.chatId);\n                const chatDocSnap = await getDoc(chatDocRef);\n                if (chatDocSnap.exists()) {\n                    var _chatConfig_systemPrompt, _chatConfig_systemPrompt1, _chatConfig_systemPrompt2, _chatConfig_context, _chatConfig_context1, _chatConfig_context2;\n                    const chatConfig = chatDocSnap.data();\n                    console.group(\"⚙️ CONFIGURA\\xc7\\xd5ES DO CHAT (FIRESTORE)\");\n                    console.log(\"\\uD83C\\uDFAF System Prompt:\", {\n                        length: ((_chatConfig_systemPrompt = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt === void 0 ? void 0 : _chatConfig_systemPrompt.length) || 0,\n                        content: chatConfig.systemPrompt || \"(vazio)\",\n                        preview: ((_chatConfig_systemPrompt1 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt1 === void 0 ? void 0 : _chatConfig_systemPrompt1.substring(0, 200)) + (((_chatConfig_systemPrompt2 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt2 === void 0 ? void 0 : _chatConfig_systemPrompt2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83D\\uDCDD Contexto:\", {\n                        length: ((_chatConfig_context = chatConfig.context) === null || _chatConfig_context === void 0 ? void 0 : _chatConfig_context.length) || 0,\n                        content: chatConfig.context || \"(vazio)\",\n                        preview: ((_chatConfig_context1 = chatConfig.context) === null || _chatConfig_context1 === void 0 ? void 0 : _chatConfig_context1.substring(0, 200)) + (((_chatConfig_context2 = chatConfig.context) === null || _chatConfig_context2 === void 0 ? void 0 : _chatConfig_context2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83C\\uDF21️ Par\\xe2metros de Gera\\xe7\\xe3o:\", {\n                        temperature: chatConfig.temperature,\n                        temperatureType: typeof chatConfig.temperature,\n                        frequencyPenalty: chatConfig.frequencyPenalty,\n                        frequencyPenaltyType: typeof chatConfig.frequencyPenalty,\n                        repetitionPenalty: chatConfig.repetitionPenalty,\n                        repetitionPenaltyType: typeof chatConfig.repetitionPenalty,\n                        maxTokens: chatConfig.maxTokens,\n                        maxTokensType: typeof chatConfig.maxTokens,\n                        latexInstructions: chatConfig.latexInstructions\n                    });\n                    console.log(\"\\uD83D\\uDCCA Metadados:\", {\n                        lastUsedModel: chatConfig.lastUsedModel,\n                        createdAt: chatConfig.createdAt,\n                        lastUpdatedAt: chatConfig.lastUpdatedAt,\n                        name: chatConfig.name\n                    });\n                    console.groupEnd();\n                } else {\n                    console.warn(\"⚠️ Documento do chat n\\xe3o encontrado no Firestore\");\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar configura\\xe7\\xf5es do chat do Firestore:\", error);\n            }\n            // Buscar histórico de mensagens do Storage para mostrar nos logs\n            console.log(\"\\uD83D\\uDD0D Buscando hist\\xf3rico de mensagens do Storage para debug...\");\n            try {\n                const messagesResponse = await fetch(\"/api/chat/\".concat(requestData.username, \"/\").concat(requestData.chatId));\n                if (messagesResponse.ok) {\n                    const chatData = await messagesResponse.json();\n                    const messages = chatData.messages || [];\n                    console.group(\"\\uD83D\\uDCAC HIST\\xd3RICO DE MENSAGENS (STORAGE)\");\n                    console.log(\"\\uD83D\\uDCCA Total de mensagens:\", messages.length);\n                    if (messages.length > 0) {\n                        var _messages_, _messages_1;\n                        console.log(\"\\uD83D\\uDCCB \\xdaltimas 5 mensagens:\", messages.slice(-5).map((msg, index)=>{\n                            var _msg_content, _msg_content1, _msg_content2, _msg_attachments;\n                            return {\n                                index: messages.length - 5 + index,\n                                id: msg.id,\n                                role: msg.role,\n                                contentLength: ((_msg_content = msg.content) === null || _msg_content === void 0 ? void 0 : _msg_content.length) || 0,\n                                contentPreview: ((_msg_content1 = msg.content) === null || _msg_content1 === void 0 ? void 0 : _msg_content1.substring(0, 100)) + (((_msg_content2 = msg.content) === null || _msg_content2 === void 0 ? void 0 : _msg_content2.length) > 100 ? \"...\" : \"\"),\n                                timestamp: msg.timestamp,\n                                hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                                attachmentCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0\n                            };\n                        }));\n                        if (messages.length > 5) {\n                            console.log(\"\\uD83D\\uDCDC Mensagens mais antigas:\", \"\".concat(messages.length - 5, \" mensagens anteriores n\\xe3o mostradas\"));\n                        }\n                        // Estatísticas do histórico\n                        const userMessages = messages.filter((msg)=>msg.role === \"user\").length;\n                        const assistantMessages = messages.filter((msg)=>msg.role === \"assistant\").length;\n                        const totalChars = messages.reduce((acc, msg)=>{\n                            var _msg_content;\n                            return acc + (((_msg_content = msg.content) === null || _msg_content === void 0 ? void 0 : _msg_content.length) || 0);\n                        }, 0);\n                        console.log(\"\\uD83D\\uDCC8 Estat\\xedsticas do Hist\\xf3rico:\", {\n                            totalMessages: messages.length,\n                            userMessages,\n                            assistantMessages,\n                            totalCharacters: totalChars,\n                            averageMessageLength: messages.length > 0 ? Math.round(totalChars / messages.length) : 0,\n                            oldestMessage: (_messages_ = messages[0]) === null || _messages_ === void 0 ? void 0 : _messages_.timestamp,\n                            newestMessage: (_messages_1 = messages[messages.length - 1]) === null || _messages_1 === void 0 ? void 0 : _messages_1.timestamp\n                        });\n                    } else {\n                        console.log(\"\\uD83D\\uDCED Nenhuma mensagem encontrada no hist\\xf3rico\");\n                    }\n                    console.groupEnd();\n                } else {\n                    console.warn(\"⚠️ Falha ao buscar mensagens - Status:\", messagesResponse.status);\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar hist\\xf3rico de mensagens:\", error);\n            }\n            console.groupEnd();\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            // DEBUG: Log da resposta\n            console.group(\"\\uD83D\\uDCE1 AI SERVICE - RESPOSTA RECEBIDA\");\n            console.log(\"✅ Status:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCCB Headers:\", Object.fromEntries(response.headers.entries()));\n            console.log(\"\\uD83D\\uDD0D Response OK:\", response.ok);\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            console.group(\"\\uD83D\\uDD04 AI SERVICE - PROCESSANDO STREAM\");\n            console.log(\"\\uD83D\\uDCD6 Reader criado:\", !!reader);\n            console.log(\"⏰ In\\xedcio do stream:\", new Date().toISOString());\n            let chunkCount = 0;\n            let totalBytes = 0;\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        console.log(\"✅ Stream finalizado\");\n                        console.log(\"\\uD83D\\uDCCA Estat\\xedsticas finais:\", {\n                            totalChunks: chunkCount,\n                            totalBytes: totalBytes,\n                            responseLength: fullResponse.length,\n                            avgChunkSize: chunkCount > 0 ? Math.round(totalBytes / chunkCount) : 0\n                        });\n                        console.groupEnd();\n                        break;\n                    }\n                    chunkCount++;\n                    totalBytes += (value === null || value === void 0 ? void 0 : value.length) || 0;\n                    // DEBUG: Log detalhado dos bytes brutos\n                    if (chunkCount <= 10) {\n                        console.log(\"\\uD83D\\uDD0D Chunk \".concat(chunkCount, \" - Bytes brutos:\"), {\n                            rawBytes: Array.from(value || []).map((b)=>b.toString(16).padStart(2, \"0\")).join(\" \"),\n                            byteLength: (value === null || value === void 0 ? void 0 : value.length) || 0,\n                            firstBytes: Array.from((value === null || value === void 0 ? void 0 : value.slice(0, 10)) || []),\n                            lastBytes: Array.from((value === null || value === void 0 ? void 0 : value.slice(-10)) || [])\n                        });\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    // DEBUG: Log detalhado do chunk decodificado\n                    if (chunkCount <= 10) {\n                        console.log(\"\\uD83D\\uDCE6 Chunk \".concat(chunkCount, \" - Decodificado:\"), {\n                            size: chunk.length,\n                            content: chunk,\n                            contentPreview: chunk.substring(0, 100) + (chunk.length > 100 ? \"...\" : \"\"),\n                            charCodes: Array.from(chunk).map((c)=>c.charCodeAt(0)),\n                            hasNonAscii: /[^\\x00-\\x7F]/.test(chunk),\n                            hasControlChars: /[\\x00-\\x1F\\x7F-\\x9F]/.test(chunk),\n                            timestamp: new Date().toISOString()\n                        });\n                    }\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                console.group(\"✅ AI SERVICE - RESPOSTA COMPLETA\");\n                console.log(\"\\uD83D\\uDCDD Resposta final:\", {\n                    length: fullResponse.length,\n                    preview: fullResponse.substring(0, 200) + (fullResponse.length > 200 ? \"...\" : \"\"),\n                    wordCount: fullResponse.split(\" \").length,\n                    timestamp: new Date().toISOString()\n                });\n                console.groupEnd();\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            console.group(\"❌ AI SERVICE - ERRO CAPTURADO\");\n            console.error(\"\\uD83D\\uDEA8 Tipo do erro:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.error(\"\\uD83D\\uDCDD Mensagem:\", error instanceof Error ? error.message : String(error));\n            console.error(\"\\uD83D\\uDD0D Stack trace:\", error instanceof Error ? error.stack : \"N/A\");\n            console.error(\"⏰ Timestamp:\", new Date().toISOString());\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    console.log(\"\\uD83D\\uDED1 Requisi\\xe7\\xe3o cancelada pelo usu\\xe1rio\");\n                    console.groupEnd();\n                    return;\n                }\n                console.error(\"\\uD83D\\uDD27 Detalhes completos:\", error);\n                console.groupEnd();\n                onError(error.message);\n            } else {\n                console.error(\"\\uD83D\\uDD27 Detalhes completos:\", error);\n                console.groupEnd();\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        var _config_message;\n        console.group(\"\\uD83D\\uDE80 AI SERVICE - SEND MESSAGE SAFE\");\n        console.log(\"\\uD83D\\uDD27 Config recebida:\", {\n            username: config.username,\n            chatId: config.chatId,\n            messageLength: ((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.length) || 0,\n            model: config.model,\n            timestamp: new Date().toISOString()\n        });\n        console.log(\"\\uD83D\\uDCCB Callbacks:\", {\n            hasOnChunk: typeof onChunk === \"function\",\n            hasOnComplete: typeof onComplete === \"function\",\n            hasOnError: typeof onError === \"function\"\n        });\n        try {\n            console.log(\"✅ Validando configura\\xe7\\xe3o...\");\n            this.validateConfig(config);\n            console.log(\"✅ Configura\\xe7\\xe3o v\\xe1lida, enviando mensagem...\");\n            await this.sendMessage(config, onChunk, onComplete, onError);\n            console.log(\"✅ Mensagem enviada com sucesso\");\n            console.groupEnd();\n        } catch (error) {\n            console.group(\"❌ ERRO EM SEND MESSAGE SAFE\");\n            console.error(\"\\uD83D\\uDEA8 Erro capturado:\", error);\n            console.error(\"\\uD83D\\uDCDD Tipo:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.groupEnd();\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n            console.groupEnd();\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});