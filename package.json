{"name": "rafthor", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/react-beautiful-dnd": "^13.1.8", "firebase": "^12.0.0", "firebase-admin": "^13.4.0", "framer-motion": "^12.23.7", "highlight.js": "^11.11.1", "katex": "^0.16.22", "lucide-react": "^0.525.0", "mermaid": "^11.9.0", "next": "^13.5.0", "prismjs": "^1.30.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0"}, "devDependencies": {"@types/node": "^20", "@types/prismjs": "^1.26.5", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10", "eslint": "^8", "eslint-config-next": "^13.5.0", "postcss": "^8", "tailwindcss": "^3", "typescript": "^5"}}