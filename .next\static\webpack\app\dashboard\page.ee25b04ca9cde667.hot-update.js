"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n// Serviço para integração com Firebase Functions de IA\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            var _requestData_message, _requestData_message1, _requestData_message2;\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model\n            };\n            // DEBUG: Log detalhado no console do navegador\n            console.group(\"\\uD83E\\uDD16 AI SERVICE - ENVIANDO REQUISI\\xc7\\xc3O\");\n            console.log(\"\\uD83D\\uDCE1 URL da Function:\", this.functionUrl);\n            console.log(\"\\uD83D\\uDCCB Dados da Requisi\\xe7\\xe3o:\", {\n                username: requestData.username,\n                chatId: requestData.chatId,\n                message: requestData.message,\n                messageLength: ((_requestData_message = requestData.message) === null || _requestData_message === void 0 ? void 0 : _requestData_message.length) || 0,\n                messagePreview: ((_requestData_message1 = requestData.message) === null || _requestData_message1 === void 0 ? void 0 : _requestData_message1.substring(0, 100)) + (((_requestData_message2 = requestData.message) === null || _requestData_message2 === void 0 ? void 0 : _requestData_message2.length) > 100 ? \"...\" : \"\"),\n                model: requestData.model,\n                timestamp: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDD27 Headers:\", {\n                \"Content-Type\": \"application/json\",\n                \"Method\": \"POST\"\n            });\n            console.log(\"\\uD83D\\uDCE6 Body JSON:\", JSON.stringify(requestData, null, 2));\n            console.log(\"\\uD83D\\uDCCA Estat\\xedsticas:\", {\n                bodySize: JSON.stringify(requestData).length + \" bytes\",\n                hasAbortController: !!this.abortController,\n                userAgent: navigator.userAgent\n            });\n            // Buscar configurações do chat para mostrar nos logs\n            console.log(\"\\uD83D\\uDD0D Buscando configura\\xe7\\xf5es do chat para debug...\");\n            try {\n                const chatConfigResponse = await fetch(\"/api/chat/\".concat(requestData.username, \"/\").concat(requestData.chatId, \"/config\"));\n                if (chatConfigResponse.ok) {\n                    var _chatConfig_systemPrompt, _chatConfig_systemPrompt1, _chatConfig_systemPrompt2, _chatConfig_context, _chatConfig_context1, _chatConfig_context2;\n                    const chatConfig = await chatConfigResponse.json();\n                    console.group(\"⚙️ CONFIGURA\\xc7\\xd5ES DO CHAT\");\n                    console.log(\"\\uD83C\\uDFAF System Prompt:\", {\n                        length: ((_chatConfig_systemPrompt = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt === void 0 ? void 0 : _chatConfig_systemPrompt.length) || 0,\n                        content: chatConfig.systemPrompt || \"(vazio)\",\n                        preview: ((_chatConfig_systemPrompt1 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt1 === void 0 ? void 0 : _chatConfig_systemPrompt1.substring(0, 200)) + (((_chatConfig_systemPrompt2 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt2 === void 0 ? void 0 : _chatConfig_systemPrompt2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83D\\uDCDD Contexto:\", {\n                        length: ((_chatConfig_context = chatConfig.context) === null || _chatConfig_context === void 0 ? void 0 : _chatConfig_context.length) || 0,\n                        content: chatConfig.context || \"(vazio)\",\n                        preview: ((_chatConfig_context1 = chatConfig.context) === null || _chatConfig_context1 === void 0 ? void 0 : _chatConfig_context1.substring(0, 200)) + (((_chatConfig_context2 = chatConfig.context) === null || _chatConfig_context2 === void 0 ? void 0 : _chatConfig_context2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83C\\uDF21️ Par\\xe2metros de Gera\\xe7\\xe3o:\", {\n                        temperature: chatConfig.temperature,\n                        temperatureType: typeof chatConfig.temperature,\n                        frequencyPenalty: chatConfig.frequencyPenalty,\n                        frequencyPenaltyType: typeof chatConfig.frequencyPenalty,\n                        repetitionPenalty: chatConfig.repetitionPenalty,\n                        repetitionPenaltyType: typeof chatConfig.repetitionPenalty,\n                        maxTokens: chatConfig.maxTokens,\n                        maxTokensType: typeof chatConfig.maxTokens,\n                        latexInstructions: chatConfig.latexInstructions\n                    });\n                    console.groupEnd();\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar configura\\xe7\\xf5es do chat:\", error);\n            }\n            // Buscar histórico de mensagens para mostrar nos logs\n            try {\n                const messagesResponse = await fetch(\"/api/chat/\".concat(requestData.username, \"/\").concat(requestData.chatId));\n                if (messagesResponse.ok) {\n                    const chatData = await messagesResponse.json();\n                    const messages = chatData.messages || [];\n                    console.group(\"\\uD83D\\uDCAC HIST\\xd3RICO DE MENSAGENS\");\n                    console.log(\"\\uD83D\\uDCCA Total de mensagens:\", messages.length);\n                    console.log(\"\\uD83D\\uDCCB \\xdaltimas 5 mensagens:\", messages.slice(-5).map((msg, index)=>{\n                        var _msg_content, _msg_content1, _msg_content2;\n                        return {\n                            index: messages.length - 5 + index,\n                            id: msg.id,\n                            role: msg.role,\n                            contentLength: ((_msg_content = msg.content) === null || _msg_content === void 0 ? void 0 : _msg_content.length) || 0,\n                            contentPreview: ((_msg_content1 = msg.content) === null || _msg_content1 === void 0 ? void 0 : _msg_content1.substring(0, 100)) + (((_msg_content2 = msg.content) === null || _msg_content2 === void 0 ? void 0 : _msg_content2.length) > 100 ? \"...\" : \"\"),\n                            timestamp: msg.timestamp\n                        };\n                    }));\n                    if (messages.length > 5) {\n                        console.log(\"\\uD83D\\uDCDC Mensagens mais antigas:\", \"\".concat(messages.length - 5, \" mensagens anteriores n\\xe3o mostradas\"));\n                    }\n                    console.groupEnd();\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar hist\\xf3rico de mensagens:\", error);\n            }\n            console.groupEnd();\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            // DEBUG: Log da resposta\n            console.group(\"\\uD83D\\uDCE1 AI SERVICE - RESPOSTA RECEBIDA\");\n            console.log(\"✅ Status:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCCB Headers:\", Object.fromEntries(response.headers.entries()));\n            console.log(\"\\uD83D\\uDD0D Response OK:\", response.ok);\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            console.group(\"\\uD83D\\uDD04 AI SERVICE - PROCESSANDO STREAM\");\n            console.log(\"\\uD83D\\uDCD6 Reader criado:\", !!reader);\n            console.log(\"⏰ In\\xedcio do stream:\", new Date().toISOString());\n            let chunkCount = 0;\n            let totalBytes = 0;\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        console.log(\"✅ Stream finalizado\");\n                        console.log(\"\\uD83D\\uDCCA Estat\\xedsticas finais:\", {\n                            totalChunks: chunkCount,\n                            totalBytes: totalBytes,\n                            responseLength: fullResponse.length,\n                            avgChunkSize: chunkCount > 0 ? Math.round(totalBytes / chunkCount) : 0\n                        });\n                        console.groupEnd();\n                        break;\n                    }\n                    chunkCount++;\n                    totalBytes += (value === null || value === void 0 ? void 0 : value.length) || 0;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    if (chunkCount <= 5) {\n                        console.log(\"\\uD83D\\uDCE6 Chunk \".concat(chunkCount, \":\"), {\n                            size: chunk.length,\n                            content: chunk.substring(0, 100) + (chunk.length > 100 ? \"...\" : \"\"),\n                            timestamp: new Date().toISOString()\n                        });\n                    }\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                console.group(\"✅ AI SERVICE - RESPOSTA COMPLETA\");\n                console.log(\"\\uD83D\\uDCDD Resposta final:\", {\n                    length: fullResponse.length,\n                    preview: fullResponse.substring(0, 200) + (fullResponse.length > 200 ? \"...\" : \"\"),\n                    wordCount: fullResponse.split(\" \").length,\n                    timestamp: new Date().toISOString()\n                });\n                console.groupEnd();\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            console.group(\"❌ AI SERVICE - ERRO CAPTURADO\");\n            console.error(\"\\uD83D\\uDEA8 Tipo do erro:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.error(\"\\uD83D\\uDCDD Mensagem:\", error instanceof Error ? error.message : String(error));\n            console.error(\"\\uD83D\\uDD0D Stack trace:\", error instanceof Error ? error.stack : \"N/A\");\n            console.error(\"⏰ Timestamp:\", new Date().toISOString());\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    console.log(\"\\uD83D\\uDED1 Requisi\\xe7\\xe3o cancelada pelo usu\\xe1rio\");\n                    console.groupEnd();\n                    return;\n                }\n                console.error(\"\\uD83D\\uDD27 Detalhes completos:\", error);\n                console.groupEnd();\n                onError(error.message);\n            } else {\n                console.error(\"\\uD83D\\uDD27 Detalhes completos:\", error);\n                console.groupEnd();\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        var _config_message;\n        console.group(\"\\uD83D\\uDE80 AI SERVICE - SEND MESSAGE SAFE\");\n        console.log(\"\\uD83D\\uDD27 Config recebida:\", {\n            username: config.username,\n            chatId: config.chatId,\n            messageLength: ((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.length) || 0,\n            model: config.model,\n            timestamp: new Date().toISOString()\n        });\n        console.log(\"\\uD83D\\uDCCB Callbacks:\", {\n            hasOnChunk: typeof onChunk === \"function\",\n            hasOnComplete: typeof onComplete === \"function\",\n            hasOnError: typeof onError === \"function\"\n        });\n        try {\n            console.log(\"✅ Validando configura\\xe7\\xe3o...\");\n            this.validateConfig(config);\n            console.log(\"✅ Configura\\xe7\\xe3o v\\xe1lida, enviando mensagem...\");\n            await this.sendMessage(config, onChunk, onComplete, onError);\n            console.log(\"✅ Mensagem enviada com sucesso\");\n            console.groupEnd();\n        } catch (error) {\n            console.group(\"❌ ERRO EM SEND MESSAGE SAFE\");\n            console.error(\"\\uD83D\\uDEA8 Erro capturado:\", error);\n            console.error(\"\\uD83D\\uDCDD Tipo:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.groupEnd();\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n            console.groupEnd();\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});