"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n// Serviço para integração com Firebase Functions de IA\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model\n            };\n            // Enviar requisição\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            // DEBUG: Log da resposta\n            console.group(\"\\uD83D\\uDCE1 AI SERVICE - RESPOSTA RECEBIDA\");\n            console.log(\"✅ Status:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCCB Headers:\", Object.fromEntries(response.headers.entries()));\n            console.log(\"\\uD83D\\uDD0D Response OK:\", response.ok);\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            // Processar stream\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    return;\n                }\n                onError(error.message);\n            } else {\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        var _config_message;\n        console.group(\"\\uD83D\\uDE80 AI SERVICE - SEND MESSAGE SAFE\");\n        console.log(\"\\uD83D\\uDD27 Config recebida:\", {\n            username: config.username,\n            chatId: config.chatId,\n            messageLength: ((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.length) || 0,\n            model: config.model,\n            timestamp: new Date().toISOString()\n        });\n        console.log(\"\\uD83D\\uDCCB Callbacks:\", {\n            hasOnChunk: typeof onChunk === \"function\",\n            hasOnComplete: typeof onComplete === \"function\",\n            hasOnError: typeof onError === \"function\"\n        });\n        try {\n            console.log(\"✅ Validando configura\\xe7\\xe3o...\");\n            this.validateConfig(config);\n            console.log(\"✅ Configura\\xe7\\xe3o v\\xe1lida, enviando mensagem...\");\n            await this.sendMessage(config, onChunk, onComplete, onError);\n            console.log(\"✅ Mensagem enviada com sucesso\");\n            console.groupEnd();\n        } catch (error) {\n            console.group(\"❌ ERRO EM SEND MESSAGE SAFE\");\n            console.error(\"\\uD83D\\uDEA8 Erro capturado:\", error);\n            console.error(\"\\uD83D\\uDCDD Tipo:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.groupEnd();\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n            console.groupEnd();\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});