'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import rehypeHighlight from 'rehype-highlight';

// Importar estilos do KaTeX
import 'katex/dist/katex.min.css';
import '@/styles/markdown-latex.css';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = React.memo(({
  content,
  className = ''
}) => {
  return (
    <div className={`markdown-content ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm, remarkMath]}
        rehypePlugins={[
          rehypeKatex,
          [rehypeHighlight, { detect: true, ignoreMissing: true }]
        ]}
        components={{
          // Customizar renderização de código
          code({ node, inline, className, children, ...props }) {
            const match = /language-(\w+)/.exec(className || '');
            return !inline && match ? (
              <pre className="bg-gray-800 rounded-lg p-4 overflow-x-auto">
                <code className={className} {...props}>
                  {children}
                </code>
              </pre>
            ) : (
              <code 
                className="bg-gray-700 px-1.5 py-0.5 rounded text-sm font-mono" 
                {...props}
              >
                {children}
              </code>
            );
          },
          
          // Customizar renderização de links
          a({ children, href, ...props }) {
            return (
              <a
                href={href}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-400 hover:text-blue-300 underline"
                {...props}
              >
                {children}
              </a>
            );
          },
          
          // Customizar renderização de tabelas
          table({ children, ...props }) {
            return (
              <div className="overflow-x-auto my-4">
                <table 
                  className="min-w-full border-collapse border border-gray-600"
                  {...props}
                >
                  {children}
                </table>
              </div>
            );
          },
          
          th({ children, ...props }) {
            return (
              <th 
                className="border border-gray-600 bg-gray-700 px-4 py-2 text-left font-semibold"
                {...props}
              >
                {children}
              </th>
            );
          },
          
          td({ children, ...props }) {
            return (
              <td 
                className="border border-gray-600 px-4 py-2"
                {...props}
              >
                {children}
              </td>
            );
          },
          
          // Customizar renderização de blockquotes
          blockquote({ children, ...props }) {
            return (
              <blockquote 
                className="border-l-4 border-blue-500 pl-4 py-2 my-4 bg-gray-800/50 italic"
                {...props}
              >
                {children}
              </blockquote>
            );
          },
          
          // Customizar renderização de listas
          ul({ children, ...props }) {
            return (
              <ul className="list-disc list-inside space-y-1 my-2" {...props}>
                {children}
              </ul>
            );
          },
          
          ol({ children, ...props }) {
            return (
              <ol className="list-decimal list-inside space-y-1 my-2" {...props}>
                {children}
              </ol>
            );
          },
          
          // Customizar renderização de títulos
          h1({ children, ...props }) {
            return (
              <h1 className="text-2xl font-bold mb-4 mt-6 text-white" {...props}>
                {children}
              </h1>
            );
          },
          
          h2({ children, ...props }) {
            return (
              <h2 className="text-xl font-bold mb-3 mt-5 text-white" {...props}>
                {children}
              </h2>
            );
          },
          
          h3({ children, ...props }) {
            return (
              <h3 className="text-lg font-bold mb-2 mt-4 text-white" {...props}>
                {children}
              </h3>
            );
          },
          
          // Customizar renderização de parágrafos
          p({ children, ...props }) {
            return (
              <p className="mb-3 leading-relaxed text-gray-200" {...props}>
                {children}
              </p>
            );
          },
          
          // Customizar renderização de linha horizontal
          hr({ ...props }) {
            return (
              <hr className="border-gray-600 my-6" {...props} />
            );
          }
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
});

export default MarkdownRenderer;
