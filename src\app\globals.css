@tailwind base;
@tailwind components;
@tailwind utilities;

/* Scrollbar customizada */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-blue-900 {
  scrollbar-color: rgba(30, 58, 138, 0.8) transparent;
}

.scrollbar-track-transparent {
  scrollbar-track-color: transparent;
}

/* Variáveis CSS */
:root {
  --sidebar-width: 280px;
  --dashboard-bg: #0f172a;
  --sidebar-bg: rgba(67, 56, 202, 0.15);
}

:root {
  --foreground-rgb: 255, 255, 255;
  --navy-blue: #0d1c4a;
  --dark-royal-blue: #1b2e76;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(135deg, var(--navy-blue) 0%, var(--dark-royal-blue) 100%);
  min-height: 100vh;
}

.bg-gradient-rafthor {
  background: linear-gradient(135deg, #0d1c4a 0%, #1b2e76 100%);
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.8);
  }
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes buttonHover {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* Utility classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.message-slide-in {
  animation: messageSlideIn 0.3s ease-out;
}

.button-hover-effect:hover {
  animation: buttonHover 0.2s ease-in-out;
}

.shimmer-effect {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

.typing-indicator {
  animation: typing 1.4s infinite ease-in-out;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-border {
  position: relative;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border: 1px solid transparent;
}

/* Estilos para Markdown */
.markdown-content {
  line-height: 1.6;
}

.markdown-content pre {
  background: rgba(13, 28, 74, 0.4) !important;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 0.5rem;
}

.markdown-content pre code {
  background: transparent !important;
  padding: 0 !important;
  font-size: 0.875rem;
  line-height: 1.5;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Highlight.js customização para tema do site */
.markdown-content .hljs {
  background: rgba(13, 28, 74, 0.6) !important;
  color: #e5e7eb;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

/* Links com hover suave */
.markdown-content a {
  color: #60a5fa;
  text-decoration: underline;
  text-decoration-color: rgba(96, 165, 250, 0.5);
  transition: all 0.2s ease;
}

.markdown-content a:hover {
  color: #93c5fd;
  text-decoration-color: #93c5fd;
}

/* Estilos para markdown e LaTeX */
.markdown-content {
  @apply text-gray-200 leading-relaxed;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  @apply text-white font-bold mb-3 mt-4;
}

.markdown-content h1 { @apply text-2xl; }
.markdown-content h2 { @apply text-xl; }
.markdown-content h3 { @apply text-lg; }

.markdown-content p {
  @apply mb-3 leading-relaxed;
}

.markdown-content ul,
.markdown-content ol {
  @apply mb-3 pl-6;
}

.markdown-content ul {
  @apply list-disc;
}

.markdown-content ol {
  @apply list-decimal;
}

.markdown-content li {
  @apply mb-1;
}

.markdown-content blockquote {
  @apply border-l-4 border-blue-500 pl-4 py-2 my-4 bg-gray-800/50 italic;
}

.markdown-content code {
  @apply bg-gray-700 px-1.5 py-0.5 rounded text-sm font-mono;
}

.markdown-content pre {
  @apply bg-gray-800 rounded-lg p-4 overflow-x-auto mb-4;
}

.markdown-content pre code {
  @apply bg-transparent p-0;
}

.markdown-content table {
  @apply min-w-full border-collapse border border-gray-600 my-4;
}

.markdown-content th {
  @apply border border-gray-600 bg-gray-700 px-4 py-2 text-left font-semibold;
}

.markdown-content td {
  @apply border border-gray-600 px-4 py-2;
}

.markdown-content a {
  @apply text-blue-400 hover:text-blue-300 underline;
}

.markdown-content hr {
  @apply border-gray-600 my-6;
}

/* Scrollbar para code blocks */
.markdown-content pre::-webkit-scrollbar {
  height: 6px;
}

.markdown-content pre::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.5);
}

.markdown-content pre::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.8);
  border-radius: 3px;
}

/* Math (KaTeX) */
.markdown-content .katex {
  font-size: 1em;
}

.markdown-content .katex-display {
  margin: 1rem 0;
  text-align: center;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg, #3b82f6, #9333ea);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

/* Scrollbar customization */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Custom slider styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3B82F6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.slider::-webkit-slider-thumb:hover {
  background: #2563EB;
  transform: scale(1.1);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3B82F6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb:hover {
  background: #2563EB;
  transform: scale(1.1);
}
