"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n// Serviço para integração com Firebase Functions de IA\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            var _requestData_message, _requestData_message1, _requestData_message2;\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model\n            };\n            // DEBUG: Log detalhado no console do navegador\n            console.group(\"\\uD83E\\uDD16 AI SERVICE - ENVIANDO REQUISI\\xc7\\xc3O\");\n            console.log(\"\\uD83D\\uDCE1 URL da Function:\", this.functionUrl);\n            console.log(\"\\uD83D\\uDCCB Dados da Requisi\\xe7\\xe3o:\", {\n                username: requestData.username,\n                chatId: requestData.chatId,\n                message: requestData.message,\n                messageLength: ((_requestData_message = requestData.message) === null || _requestData_message === void 0 ? void 0 : _requestData_message.length) || 0,\n                messagePreview: ((_requestData_message1 = requestData.message) === null || _requestData_message1 === void 0 ? void 0 : _requestData_message1.substring(0, 100)) + (((_requestData_message2 = requestData.message) === null || _requestData_message2 === void 0 ? void 0 : _requestData_message2.length) > 100 ? \"...\" : \"\"),\n                model: requestData.model,\n                timestamp: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDD27 Headers:\", {\n                \"Content-Type\": \"application/json\",\n                \"Method\": \"POST\"\n            });\n            console.log(\"\\uD83D\\uDCE6 Body JSON:\", JSON.stringify(requestData, null, 2));\n            console.log(\"\\uD83D\\uDCCA Estat\\xedsticas:\", {\n                bodySize: JSON.stringify(requestData).length + \" bytes\",\n                hasAbortController: !!this.abortController,\n                userAgent: navigator.userAgent\n            });\n            console.groupEnd();\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            // DEBUG: Log da resposta\n            console.group(\"\\uD83D\\uDCE1 AI SERVICE - RESPOSTA RECEBIDA\");\n            console.log(\"✅ Status:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCCB Headers:\", Object.fromEntries(response.headers.entries()));\n            console.log(\"\\uD83D\\uDD0D Response OK:\", response.ok);\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            console.group(\"\\uD83D\\uDD04 AI SERVICE - PROCESSANDO STREAM\");\n            console.log(\"\\uD83D\\uDCD6 Reader criado:\", !!reader);\n            console.log(\"⏰ In\\xedcio do stream:\", new Date().toISOString());\n            let chunkCount = 0;\n            let totalBytes = 0;\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        console.log(\"✅ Stream finalizado\");\n                        console.log(\"\\uD83D\\uDCCA Estat\\xedsticas finais:\", {\n                            totalChunks: chunkCount,\n                            totalBytes: totalBytes,\n                            responseLength: fullResponse.length,\n                            avgChunkSize: chunkCount > 0 ? Math.round(totalBytes / chunkCount) : 0\n                        });\n                        console.groupEnd();\n                        break;\n                    }\n                    chunkCount++;\n                    totalBytes += (value === null || value === void 0 ? void 0 : value.length) || 0;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    if (chunkCount <= 5) {\n                        console.log(\"\\uD83D\\uDCE6 Chunk \".concat(chunkCount, \":\"), {\n                            size: chunk.length,\n                            content: chunk.substring(0, 100) + (chunk.length > 100 ? \"...\" : \"\"),\n                            timestamp: new Date().toISOString()\n                        });\n                    }\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                console.group(\"✅ AI SERVICE - RESPOSTA COMPLETA\");\n                console.log(\"\\uD83D\\uDCDD Resposta final:\", {\n                    length: fullResponse.length,\n                    preview: fullResponse.substring(0, 200) + (fullResponse.length > 200 ? \"...\" : \"\"),\n                    wordCount: fullResponse.split(\" \").length,\n                    timestamp: new Date().toISOString()\n                });\n                console.groupEnd();\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            console.group(\"❌ AI SERVICE - ERRO CAPTURADO\");\n            console.error(\"\\uD83D\\uDEA8 Tipo do erro:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.error(\"\\uD83D\\uDCDD Mensagem:\", error instanceof Error ? error.message : String(error));\n            console.error(\"\\uD83D\\uDD0D Stack trace:\", error instanceof Error ? error.stack : \"N/A\");\n            console.error(\"⏰ Timestamp:\", new Date().toISOString());\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    console.log(\"\\uD83D\\uDED1 Requisi\\xe7\\xe3o cancelada pelo usu\\xe1rio\");\n                    console.groupEnd();\n                    return;\n                }\n                console.error(\"\\uD83D\\uDD27 Detalhes completos:\", error);\n                console.groupEnd();\n                onError(error.message);\n            } else {\n                console.error(\"\\uD83D\\uDD27 Detalhes completos:\", error);\n                console.groupEnd();\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        var _config_message;\n        console.group(\"\\uD83D\\uDE80 AI SERVICE - SEND MESSAGE SAFE\");\n        console.log(\"\\uD83D\\uDD27 Config recebida:\", {\n            username: config.username,\n            chatId: config.chatId,\n            messageLength: ((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.length) || 0,\n            model: config.model,\n            timestamp: new Date().toISOString()\n        });\n        console.log(\"\\uD83D\\uDCCB Callbacks:\", {\n            hasOnChunk: typeof onChunk === \"function\",\n            hasOnComplete: typeof onComplete === \"function\",\n            hasOnError: typeof onError === \"function\"\n        });\n        try {\n            console.log(\"✅ Validando configura\\xe7\\xe3o...\");\n            this.validateConfig(config);\n            console.log(\"✅ Configura\\xe7\\xe3o v\\xe1lida, enviando mensagem...\");\n            await this.sendMessage(config, onChunk, onComplete, onError);\n            console.log(\"✅ Mensagem enviada com sucesso\");\n            console.groupEnd();\n        } catch (error) {\n            console.group(\"❌ ERRO EM SEND MESSAGE SAFE\");\n            console.error(\"\\uD83D\\uDEA8 Erro capturado:\", error);\n            console.error(\"\\uD83D\\uDCDD Tipo:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.groupEnd();\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n            console.groupEnd();\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});