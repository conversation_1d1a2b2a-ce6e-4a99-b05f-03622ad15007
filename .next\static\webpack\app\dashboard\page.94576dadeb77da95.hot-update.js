"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n// Serviço para integração com Firebase Functions de IA\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model\n            };\n            // Enviar requisição\n            // Buscar configurações do chat do Firestore para mostrar nos logs\n            console.log(\"\\uD83D\\uDD0D Buscando configura\\xe7\\xf5es do chat do Firestore para debug...\");\n            try {\n                // Importar Firebase dinamicamente para evitar problemas de SSR\n                const { doc, getDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                const { db } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\"));\n                const chatDocRef = doc(db, \"usuarios\", requestData.username, \"conversas\", requestData.chatId);\n                const chatDocSnap = await getDoc(chatDocRef);\n                if (chatDocSnap.exists()) {\n                    var _chatConfig_systemPrompt, _chatConfig_systemPrompt1, _chatConfig_systemPrompt2, _chatConfig_context, _chatConfig_context1, _chatConfig_context2;\n                    const chatConfig = chatDocSnap.data();\n                    console.group(\"⚙️ CONFIGURA\\xc7\\xd5ES DO CHAT (FIRESTORE)\");\n                    console.log(\"\\uD83C\\uDFAF System Prompt:\", {\n                        length: ((_chatConfig_systemPrompt = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt === void 0 ? void 0 : _chatConfig_systemPrompt.length) || 0,\n                        content: chatConfig.systemPrompt || \"(vazio)\",\n                        preview: ((_chatConfig_systemPrompt1 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt1 === void 0 ? void 0 : _chatConfig_systemPrompt1.substring(0, 200)) + (((_chatConfig_systemPrompt2 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt2 === void 0 ? void 0 : _chatConfig_systemPrompt2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83D\\uDCDD Contexto:\", {\n                        length: ((_chatConfig_context = chatConfig.context) === null || _chatConfig_context === void 0 ? void 0 : _chatConfig_context.length) || 0,\n                        content: chatConfig.context || \"(vazio)\",\n                        preview: ((_chatConfig_context1 = chatConfig.context) === null || _chatConfig_context1 === void 0 ? void 0 : _chatConfig_context1.substring(0, 200)) + (((_chatConfig_context2 = chatConfig.context) === null || _chatConfig_context2 === void 0 ? void 0 : _chatConfig_context2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83C\\uDF21️ Par\\xe2metros de Gera\\xe7\\xe3o:\", {\n                        temperature: chatConfig.temperature,\n                        temperatureType: typeof chatConfig.temperature,\n                        frequencyPenalty: chatConfig.frequencyPenalty,\n                        frequencyPenaltyType: typeof chatConfig.frequencyPenalty,\n                        repetitionPenalty: chatConfig.repetitionPenalty,\n                        repetitionPenaltyType: typeof chatConfig.repetitionPenalty,\n                        maxTokens: chatConfig.maxTokens,\n                        maxTokensType: typeof chatConfig.maxTokens,\n                        latexInstructions: chatConfig.latexInstructions\n                    });\n                    console.log(\"\\uD83D\\uDCCA Metadados:\", {\n                        lastUsedModel: chatConfig.lastUsedModel,\n                        createdAt: chatConfig.createdAt,\n                        lastUpdatedAt: chatConfig.lastUpdatedAt,\n                        name: chatConfig.name\n                    });\n                    console.groupEnd();\n                } else {\n                    console.warn(\"⚠️ Documento do chat n\\xe3o encontrado no Firestore\");\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar configura\\xe7\\xf5es do chat do Firestore:\", error);\n            }\n            // Buscar histórico de mensagens do Storage para mostrar nos logs\n            console.log(\"\\uD83D\\uDD0D Buscando hist\\xf3rico de mensagens do Storage para debug...\");\n            try {\n                const messagesResponse = await fetch(\"/api/chat/\".concat(requestData.username, \"/\").concat(requestData.chatId));\n                if (messagesResponse.ok) {\n                    const chatData = await messagesResponse.json();\n                    const messages = chatData.messages || [];\n                    console.group(\"\\uD83D\\uDCAC HIST\\xd3RICO DE MENSAGENS (STORAGE)\");\n                    console.log(\"\\uD83D\\uDCCA Total de mensagens:\", messages.length);\n                    if (messages.length > 0) {\n                        var _messages_, _messages_1;\n                        console.log(\"\\uD83D\\uDCCB \\xdaltimas 5 mensagens:\", messages.slice(-5).map((msg, index)=>{\n                            var _msg_content, _msg_content1, _msg_content2, _msg_attachments;\n                            return {\n                                index: messages.length - 5 + index,\n                                id: msg.id,\n                                role: msg.role,\n                                contentLength: ((_msg_content = msg.content) === null || _msg_content === void 0 ? void 0 : _msg_content.length) || 0,\n                                contentPreview: ((_msg_content1 = msg.content) === null || _msg_content1 === void 0 ? void 0 : _msg_content1.substring(0, 100)) + (((_msg_content2 = msg.content) === null || _msg_content2 === void 0 ? void 0 : _msg_content2.length) > 100 ? \"...\" : \"\"),\n                                timestamp: msg.timestamp,\n                                hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                                attachmentCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0\n                            };\n                        }));\n                        if (messages.length > 5) {\n                            console.log(\"\\uD83D\\uDCDC Mensagens mais antigas:\", \"\".concat(messages.length - 5, \" mensagens anteriores n\\xe3o mostradas\"));\n                        }\n                        // Estatísticas do histórico\n                        const userMessages = messages.filter((msg)=>msg.role === \"user\").length;\n                        const assistantMessages = messages.filter((msg)=>msg.role === \"assistant\").length;\n                        const totalChars = messages.reduce((acc, msg)=>{\n                            var _msg_content;\n                            return acc + (((_msg_content = msg.content) === null || _msg_content === void 0 ? void 0 : _msg_content.length) || 0);\n                        }, 0);\n                        console.log(\"\\uD83D\\uDCC8 Estat\\xedsticas do Hist\\xf3rico:\", {\n                            totalMessages: messages.length,\n                            userMessages,\n                            assistantMessages,\n                            totalCharacters: totalChars,\n                            averageMessageLength: messages.length > 0 ? Math.round(totalChars / messages.length) : 0,\n                            oldestMessage: (_messages_ = messages[0]) === null || _messages_ === void 0 ? void 0 : _messages_.timestamp,\n                            newestMessage: (_messages_1 = messages[messages.length - 1]) === null || _messages_1 === void 0 ? void 0 : _messages_1.timestamp\n                        });\n                    } else {\n                        console.log(\"\\uD83D\\uDCED Nenhuma mensagem encontrada no hist\\xf3rico\");\n                    }\n                    console.groupEnd();\n                } else {\n                    console.warn(\"⚠️ Falha ao buscar mensagens - Status:\", messagesResponse.status);\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar hist\\xf3rico de mensagens:\", error);\n            }\n            console.groupEnd();\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            // DEBUG: Log da resposta\n            console.group(\"\\uD83D\\uDCE1 AI SERVICE - RESPOSTA RECEBIDA\");\n            console.log(\"✅ Status:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCCB Headers:\", Object.fromEntries(response.headers.entries()));\n            console.log(\"\\uD83D\\uDD0D Response OK:\", response.ok);\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            // Processar stream\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    return;\n                }\n                onError(error.message);\n            } else {\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        var _config_message;\n        console.group(\"\\uD83D\\uDE80 AI SERVICE - SEND MESSAGE SAFE\");\n        console.log(\"\\uD83D\\uDD27 Config recebida:\", {\n            username: config.username,\n            chatId: config.chatId,\n            messageLength: ((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.length) || 0,\n            model: config.model,\n            timestamp: new Date().toISOString()\n        });\n        console.log(\"\\uD83D\\uDCCB Callbacks:\", {\n            hasOnChunk: typeof onChunk === \"function\",\n            hasOnComplete: typeof onComplete === \"function\",\n            hasOnError: typeof onError === \"function\"\n        });\n        try {\n            console.log(\"✅ Validando configura\\xe7\\xe3o...\");\n            this.validateConfig(config);\n            console.log(\"✅ Configura\\xe7\\xe3o v\\xe1lida, enviando mensagem...\");\n            await this.sendMessage(config, onChunk, onComplete, onError);\n            console.log(\"✅ Mensagem enviada com sucesso\");\n            console.groupEnd();\n        } catch (error) {\n            console.group(\"❌ ERRO EM SEND MESSAGE SAFE\");\n            console.error(\"\\uD83D\\uDEA8 Erro capturado:\", error);\n            console.error(\"\\uD83D\\uDCDD Tipo:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.groupEnd();\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n            console.groupEnd();\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc2VydmljZXMvYWlTZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSx1REFBdUQ7QUF1QnZELE1BQU1BO0lBSUo7O0dBRUMsR0FDRCxNQUFNQyxZQUNKQyxNQUF1QixFQUN2QkMsT0FBZ0MsRUFDaENDLFVBQTBDLEVBQzFDQyxPQUFnQyxFQUNqQjtRQUNmLElBQUk7WUFDRixrREFBa0Q7WUFDbEQsSUFBSSxDQUFDQyxlQUFlLEdBQUcsSUFBSUM7WUFFM0IsK0JBQStCO1lBQy9CLE1BQU1DLGNBQWM7Z0JBQ2xCQyxVQUFVUCxPQUFPTyxRQUFRO2dCQUN6QkMsUUFBUVIsT0FBT1EsTUFBTTtnQkFDckJDLFNBQVNULE9BQU9TLE9BQU87Z0JBQ3ZCQyxPQUFPVixPQUFPVSxLQUFLO1lBQ3JCO1lBRUEsb0JBQW9CO1lBRXBCLGtFQUFrRTtZQUNsRUMsUUFBUUMsR0FBRyxDQUFDO1lBQ1osSUFBSTtnQkFDRiwrREFBK0Q7Z0JBQy9ELE1BQU0sRUFBRUMsR0FBRyxFQUFFQyxNQUFNLEVBQUUsR0FBRyxNQUFNLDZMQUFPO2dCQUNyQyxNQUFNLEVBQUVDLEVBQUUsRUFBRSxHQUFHLE1BQU0sdUpBQU87Z0JBRTVCLE1BQU1DLGFBQWFILElBQUlFLElBQUksWUFBWVQsWUFBWUMsUUFBUSxFQUFFLGFBQWFELFlBQVlFLE1BQU07Z0JBQzVGLE1BQU1TLGNBQWMsTUFBTUgsT0FBT0U7Z0JBRWpDLElBQUlDLFlBQVlDLE1BQU0sSUFBSTt3QkFJZEMsMEJBRUNBLDJCQUE4Q0EsMkJBRy9DQSxxQkFFQ0Esc0JBQXlDQTtvQkFWcEQsTUFBTUEsYUFBYUYsWUFBWUcsSUFBSTtvQkFDbkNULFFBQVFVLEtBQUssQ0FBQztvQkFDZFYsUUFBUUMsR0FBRyxDQUFDLCtCQUFxQjt3QkFDL0JVLFFBQVFILEVBQUFBLDJCQUFBQSxXQUFXSSxZQUFZLGNBQXZCSiwrQ0FBQUEseUJBQXlCRyxNQUFNLEtBQUk7d0JBQzNDRSxTQUFTTCxXQUFXSSxZQUFZLElBQUk7d0JBQ3BDRSxTQUFTTixFQUFBQSw0QkFBQUEsV0FBV0ksWUFBWSxjQUF2QkosZ0RBQUFBLDBCQUF5Qk8sU0FBUyxDQUFDLEdBQUcsUUFBUVAsQ0FBQUEsRUFBQUEsNEJBQUFBLFdBQVdJLFlBQVksY0FBdkJKLGdEQUFBQSwwQkFBeUJHLE1BQU0sSUFBRyxNQUFNLFFBQVEsRUFBQztvQkFDMUc7b0JBQ0FYLFFBQVFDLEdBQUcsQ0FBQywwQkFBZ0I7d0JBQzFCVSxRQUFRSCxFQUFBQSxzQkFBQUEsV0FBV1EsT0FBTyxjQUFsQlIsMENBQUFBLG9CQUFvQkcsTUFBTSxLQUFJO3dCQUN0Q0UsU0FBU0wsV0FBV1EsT0FBTyxJQUFJO3dCQUMvQkYsU0FBU04sRUFBQUEsdUJBQUFBLFdBQVdRLE9BQU8sY0FBbEJSLDJDQUFBQSxxQkFBb0JPLFNBQVMsQ0FBQyxHQUFHLFFBQVFQLENBQUFBLEVBQUFBLHVCQUFBQSxXQUFXUSxPQUFPLGNBQWxCUiwyQ0FBQUEscUJBQW9CRyxNQUFNLElBQUcsTUFBTSxRQUFRLEVBQUM7b0JBQ2hHO29CQUNBWCxRQUFRQyxHQUFHLENBQUMsaURBQThCO3dCQUN4Q2dCLGFBQWFULFdBQVdTLFdBQVc7d0JBQ25DQyxpQkFBaUIsT0FBT1YsV0FBV1MsV0FBVzt3QkFDOUNFLGtCQUFrQlgsV0FBV1csZ0JBQWdCO3dCQUM3Q0Msc0JBQXNCLE9BQU9aLFdBQVdXLGdCQUFnQjt3QkFDeERFLG1CQUFtQmIsV0FBV2EsaUJBQWlCO3dCQUMvQ0MsdUJBQXVCLE9BQU9kLFdBQVdhLGlCQUFpQjt3QkFDMURFLFdBQVdmLFdBQVdlLFNBQVM7d0JBQy9CQyxlQUFlLE9BQU9oQixXQUFXZSxTQUFTO3dCQUMxQ0UsbUJBQW1CakIsV0FBV2lCLGlCQUFpQjtvQkFDakQ7b0JBQ0F6QixRQUFRQyxHQUFHLENBQUMsMkJBQWlCO3dCQUMzQnlCLGVBQWVsQixXQUFXa0IsYUFBYTt3QkFDdkNDLFdBQVduQixXQUFXbUIsU0FBUzt3QkFDL0JDLGVBQWVwQixXQUFXb0IsYUFBYTt3QkFDdkNDLE1BQU1yQixXQUFXcUIsSUFBSTtvQkFDdkI7b0JBQ0E3QixRQUFROEIsUUFBUTtnQkFDbEIsT0FBTztvQkFDTDlCLFFBQVErQixJQUFJLENBQUM7Z0JBQ2Y7WUFDRixFQUFFLE9BQU9DLE9BQU87Z0JBQ2RoQyxRQUFRK0IsSUFBSSxDQUFDLDhFQUFrRUM7WUFDakY7WUFFQSxpRUFBaUU7WUFDakVoQyxRQUFRQyxHQUFHLENBQUM7WUFDWixJQUFJO2dCQUNGLE1BQU1nQyxtQkFBbUIsTUFBTUMsTUFBTSxhQUFxQ3ZDLE9BQXhCQSxZQUFZQyxRQUFRLEVBQUMsS0FBc0IsT0FBbkJELFlBQVlFLE1BQU07Z0JBQzVGLElBQUlvQyxpQkFBaUJFLEVBQUUsRUFBRTtvQkFDdkIsTUFBTUMsV0FBVyxNQUFNSCxpQkFBaUJJLElBQUk7b0JBQzVDLE1BQU1DLFdBQVdGLFNBQVNFLFFBQVEsSUFBSSxFQUFFO29CQUN4Q3RDLFFBQVFVLEtBQUssQ0FBQztvQkFDZFYsUUFBUUMsR0FBRyxDQUFDLG9DQUEwQnFDLFNBQVMzQixNQUFNO29CQUVyRCxJQUFJMkIsU0FBUzNCLE1BQU0sR0FBRyxHQUFHOzRCQTJCTjJCLFlBQ0FBO3dCQTNCakJ0QyxRQUFRQyxHQUFHLENBQUMsd0NBQTJCcUMsU0FBU0MsS0FBSyxDQUFDLENBQUMsR0FBR0MsR0FBRyxDQUFDLENBQUNDLEtBQVVDO2dDQUl4REQsY0FDQ0EsZUFBa0NBLGVBR2pDQTttQ0FSeUU7Z0NBQzFGQyxPQUFPSixTQUFTM0IsTUFBTSxHQUFHLElBQUkrQjtnQ0FDN0JDLElBQUlGLElBQUlFLEVBQUU7Z0NBQ1ZDLE1BQU1ILElBQUlHLElBQUk7Z0NBQ2RDLGVBQWVKLEVBQUFBLGVBQUFBLElBQUk1QixPQUFPLGNBQVg0QixtQ0FBQUEsYUFBYTlCLE1BQU0sS0FBSTtnQ0FDdENtQyxnQkFBZ0JMLEVBQUFBLGdCQUFBQSxJQUFJNUIsT0FBTyxjQUFYNEIsb0NBQUFBLGNBQWExQixTQUFTLENBQUMsR0FBRyxRQUFRMEIsQ0FBQUEsRUFBQUEsZ0JBQUFBLElBQUk1QixPQUFPLGNBQVg0QixvQ0FBQUEsY0FBYTlCLE1BQU0sSUFBRyxNQUFNLFFBQVEsRUFBQztnQ0FDdkZvQyxXQUFXTixJQUFJTSxTQUFTO2dDQUN4QkMsZ0JBQWdCLENBQUMsQ0FBRVAsQ0FBQUEsSUFBSVEsV0FBVyxJQUFJUixJQUFJUSxXQUFXLENBQUN0QyxNQUFNLEdBQUc7Z0NBQy9EdUMsaUJBQWlCVCxFQUFBQSxtQkFBQUEsSUFBSVEsV0FBVyxjQUFmUix1Q0FBQUEsaUJBQWlCOUIsTUFBTSxLQUFJOzRCQUM5Qzs7d0JBRUEsSUFBSTJCLFNBQVMzQixNQUFNLEdBQUcsR0FBRzs0QkFDdkJYLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBOEIsR0FBdUIsT0FBcEJxQyxTQUFTM0IsTUFBTSxHQUFHLEdBQUU7d0JBQ25FO3dCQUVBLDRCQUE0Qjt3QkFDNUIsTUFBTXdDLGVBQWViLFNBQVNjLE1BQU0sQ0FBQyxDQUFDWCxNQUFhQSxJQUFJRyxJQUFJLEtBQUssUUFBUWpDLE1BQU07d0JBQzlFLE1BQU0wQyxvQkFBb0JmLFNBQVNjLE1BQU0sQ0FBQyxDQUFDWCxNQUFhQSxJQUFJRyxJQUFJLEtBQUssYUFBYWpDLE1BQU07d0JBQ3hGLE1BQU0yQyxhQUFhaEIsU0FBU2lCLE1BQU0sQ0FBQyxDQUFDQyxLQUFhZjtnQ0FBb0JBO21DQUFQZSxNQUFPZixDQUFBQSxFQUFBQSxlQUFBQSxJQUFJNUIsT0FBTyxjQUFYNEIsbUNBQUFBLGFBQWE5QixNQUFNLEtBQUk7MkJBQUk7d0JBRWhHWCxRQUFRQyxHQUFHLENBQUMsaURBQWlDOzRCQUMzQ3dELGVBQWVuQixTQUFTM0IsTUFBTTs0QkFDOUJ3Qzs0QkFDQUU7NEJBQ0FLLGlCQUFpQko7NEJBQ2pCSyxzQkFBc0JyQixTQUFTM0IsTUFBTSxHQUFHLElBQUlpRCxLQUFLQyxLQUFLLENBQUNQLGFBQWFoQixTQUFTM0IsTUFBTSxJQUFJOzRCQUN2Rm1ELGFBQWEsR0FBRXhCLGFBQUFBLFFBQVEsQ0FBQyxFQUFFLGNBQVhBLGlDQUFBQSxXQUFhUyxTQUFTOzRCQUNyQ2dCLGFBQWEsR0FBRXpCLGNBQUFBLFFBQVEsQ0FBQ0EsU0FBUzNCLE1BQU0sR0FBRyxFQUFFLGNBQTdCMkIsa0NBQUFBLFlBQStCUyxTQUFTO3dCQUN6RDtvQkFDRixPQUFPO3dCQUNML0MsUUFBUUMsR0FBRyxDQUFDO29CQUNkO29CQUVBRCxRQUFROEIsUUFBUTtnQkFDbEIsT0FBTztvQkFDTDlCLFFBQVErQixJQUFJLENBQUMsMENBQTBDRSxpQkFBaUIrQixNQUFNO2dCQUNoRjtZQUNGLEVBQUUsT0FBT2hDLE9BQU87Z0JBQ2RoQyxRQUFRK0IsSUFBSSxDQUFDLCtEQUFzREM7WUFDckU7WUFFQWhDLFFBQVE4QixRQUFRO1lBRWhCLE1BQU1tQyxXQUFXLE1BQU0vQixNQUFNLElBQUksQ0FBQ2dDLFdBQVcsRUFBRTtnQkFDN0NDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDNUU7Z0JBQ3JCNkUsUUFBUSxJQUFJLENBQUMvRSxlQUFlLENBQUMrRSxNQUFNO1lBQ3JDO1lBRUEseUJBQXlCO1lBQ3pCeEUsUUFBUVUsS0FBSyxDQUFDO1lBQ2RWLFFBQVFDLEdBQUcsQ0FBQyxhQUFhZ0UsU0FBU0QsTUFBTSxFQUFFQyxTQUFTUSxVQUFVO1lBQzdEekUsUUFBUUMsR0FBRyxDQUFDLHlCQUFleUUsT0FBT0MsV0FBVyxDQUFDVixTQUFTRyxPQUFPLENBQUNRLE9BQU87WUFDdEU1RSxRQUFRQyxHQUFHLENBQUMsNkJBQW1CZ0UsU0FBUzlCLEVBQUU7WUFDMUNuQyxRQUFRQyxHQUFHLENBQUMscUJBQVdnRSxTQUFTWSxHQUFHO1lBQ25DN0UsUUFBUThCLFFBQVE7WUFFaEIsSUFBSSxDQUFDbUMsU0FBUzlCLEVBQUUsRUFBRTtnQkFDaEIsTUFBTTJDLFlBQVksTUFBTWIsU0FBUzVCLElBQUk7Z0JBQ3JDckMsUUFBUVUsS0FBSyxDQUFDO2dCQUNkVixRQUFRZ0MsS0FBSyxDQUFDLFdBQVdpQyxTQUFTRCxNQUFNO2dCQUN4Q2hFLFFBQVFnQyxLQUFLLENBQUMsZ0JBQWdCaUMsU0FBU1EsVUFBVTtnQkFDakR6RSxRQUFRZ0MsS0FBSyxDQUFDLGVBQWU4QztnQkFDN0I5RSxRQUFROEIsUUFBUTtnQkFDaEIsTUFBTSxJQUFJaUQsTUFBTUQsVUFBVTlDLEtBQUssSUFBSSxRQUE0QmlDLE9BQXBCQSxTQUFTRCxNQUFNLEVBQUMsTUFBd0IsT0FBcEJDLFNBQVNRLFVBQVU7WUFDcEY7WUFFQSxJQUFJLENBQUNSLFNBQVNJLElBQUksRUFBRTtnQkFDbEIsTUFBTSxJQUFJVSxNQUFNO1lBQ2xCO1lBRUEsbUJBQW1CO1lBQ25CLE1BQU1DLFNBQVNmLFNBQVNJLElBQUksQ0FBQ1ksU0FBUztZQUN0QyxNQUFNQyxVQUFVLElBQUlDO1lBQ3BCLElBQUlDLGVBQWU7WUFFbkIsbUJBQW1CO1lBRW5CLElBQUk7Z0JBQ0YsTUFBTyxLQUFNO29CQUNYLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNTixPQUFPTyxJQUFJO29CQUV6QyxJQUFJRixNQUFNO3dCQUNSO29CQUNGO29CQUVBLE1BQU1HLFFBQVFOLFFBQVFPLE1BQU0sQ0FBQ0gsT0FBTzt3QkFBRUksUUFBUTtvQkFBSztvQkFFbkROLGdCQUFnQkk7b0JBRWhCLGtDQUFrQztvQkFDbENsRyxRQUFRa0c7Z0JBQ1Y7Z0JBRUEsK0JBQStCO2dCQUMvQmpHLFdBQVc2RjtZQUViLFNBQVU7Z0JBQ1JKLE9BQU9XLFdBQVc7WUFDcEI7UUFFRixFQUFFLE9BQU8zRCxPQUFPO1lBQ2QsSUFBSUEsaUJBQWlCK0MsT0FBTztnQkFDMUIsSUFBSS9DLE1BQU1ILElBQUksS0FBSyxjQUFjO29CQUMvQjtnQkFDRjtnQkFDQXJDLFFBQVF3QyxNQUFNbEMsT0FBTztZQUN2QixPQUFPO2dCQUNMTixRQUFRO1lBQ1Y7UUFDRixTQUFVO1lBQ1IsSUFBSSxDQUFDQyxlQUFlLEdBQUc7UUFDekI7SUFDRjtJQUVBOztHQUVDLEdBQ0RtRyxnQkFBc0I7UUFDcEIsSUFBSSxJQUFJLENBQUNuRyxlQUFlLEVBQUU7WUFDeEIsSUFBSSxDQUFDQSxlQUFlLENBQUNvRyxLQUFLO1lBQzFCLElBQUksQ0FBQ3BHLGVBQWUsR0FBRztRQUN6QjtJQUNGO0lBRUE7O0dBRUMsR0FDRHFHLHNCQUErQjtRQUM3QixPQUFPLElBQUksQ0FBQ3JHLGVBQWUsS0FBSztJQUNsQztJQUVBOztHQUVDLEdBQ0QsTUFBTXNHLGlCQUFpQm5HLFFBQWdCLEVBQUVDLE1BQWMsRUFBMEI7UUFDL0UsSUFBSTtZQUNGLE1BQU1vRSxXQUFXLE1BQU0vQixNQUFNLGFBQXlCckMsT0FBWkQsVUFBUyxLQUFVLE9BQVBDLFNBQVU7Z0JBQzlEc0UsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFFQSxJQUFJLENBQUNILFNBQVM5QixFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSTRDLE1BQU0sMEJBQThDLE9BQXBCZCxTQUFTUSxVQUFVO1lBQy9EO1lBRUEsTUFBTXJDLFdBQVcsTUFBTTZCLFNBQVM1QixJQUFJO1lBQ3BDLE9BQU9ELFNBQVNFLFFBQVEsSUFBSSxFQUFFO1FBRWhDLEVBQUUsT0FBT04sT0FBTztZQUNkaEMsUUFBUWdDLEtBQUssQ0FBQyx1Q0FBdUNBO1lBQ3JELE9BQU8sRUFBRTtRQUNYO0lBQ0Y7SUFFQTs7R0FFQyxHQUNEZ0Usa0JBQWtCMUQsUUFBZSxFQUFpQjtRQUNoRCxPQUFPQSxTQUFTRSxHQUFHLENBQUNDLENBQUFBLE1BQVE7Z0JBQzFCRSxJQUFJRixJQUFJRSxFQUFFO2dCQUNWOUIsU0FBUzRCLElBQUk1QixPQUFPO2dCQUNwQitCLE1BQU1ILElBQUl3RCxNQUFNLEtBQUssU0FBUyxTQUFTO2dCQUN2Q2xELFdBQVdOLElBQUlNLFNBQVM7Z0JBQ3hCbUQsWUFBWXpELElBQUl5RCxVQUFVLElBQUk7WUFDaEM7SUFDRjtJQUVBOztHQUVDLEdBQ0RDLG9CQUFvQjdELFFBQXVCLEVBQVM7UUFDbEQsT0FBT0EsU0FBU0UsR0FBRyxDQUFDQyxDQUFBQSxNQUFRO2dCQUMxQkUsSUFBSUYsSUFBSUUsRUFBRTtnQkFDVjlCLFNBQVM0QixJQUFJNUIsT0FBTztnQkFDcEJvRixRQUFReEQsSUFBSUcsSUFBSSxLQUFLLFNBQVMsU0FBUztnQkFDdkNHLFdBQVdOLElBQUlNLFNBQVM7Z0JBQ3hCbUQsWUFBWXpELElBQUl5RCxVQUFVLElBQUk7WUFDaEM7SUFDRjtJQUVBOztHQUVDLEdBQ0RFLG9CQUE0QjtRQUMxQixPQUFPQyxLQUFLQyxHQUFHLEdBQUdDLFFBQVEsQ0FBQyxNQUFNM0MsS0FBSzRDLE1BQU0sR0FBR0QsUUFBUSxDQUFDLElBQUl4RixTQUFTLENBQUM7SUFDeEU7SUFFQTs7R0FFQyxHQUNELGVBQXVCMUIsTUFBdUIsRUFBUTtZQUMvQ0Esa0JBR0FBLGdCQUdBQTtRQU5MLElBQUksR0FBQ0EsbUJBQUFBLE9BQU9PLFFBQVEsY0FBZlAsdUNBQUFBLGlCQUFpQnFILElBQUksS0FBSTtZQUM1QixNQUFNLElBQUkzQixNQUFNO1FBQ2xCO1FBQ0EsSUFBSSxHQUFDMUYsaUJBQUFBLE9BQU9RLE1BQU0sY0FBYlIscUNBQUFBLGVBQWVxSCxJQUFJLEtBQUk7WUFDMUIsTUFBTSxJQUFJM0IsTUFBTTtRQUNsQjtRQUNBLElBQUksR0FBQzFGLGtCQUFBQSxPQUFPUyxPQUFPLGNBQWRULHNDQUFBQSxnQkFBZ0JxSCxJQUFJLEtBQUk7WUFDM0IsTUFBTSxJQUFJM0IsTUFBTTtRQUNsQjtRQUNBLElBQUkxRixPQUFPUyxPQUFPLENBQUNhLE1BQU0sR0FBRyxPQUFPO1lBQ2pDLE1BQU0sSUFBSW9FLE1BQU07UUFDbEI7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTTRCLGdCQUNKdEgsTUFBdUIsRUFDdkJDLE9BQWdDLEVBQ2hDQyxVQUEwQyxFQUMxQ0MsT0FBZ0MsRUFDakI7WUFLRUg7UUFKakJXLFFBQVFVLEtBQUssQ0FBQztRQUNkVixRQUFRQyxHQUFHLENBQUMsaUNBQXVCO1lBQ2pDTCxVQUFVUCxPQUFPTyxRQUFRO1lBQ3pCQyxRQUFRUixPQUFPUSxNQUFNO1lBQ3JCK0csZUFBZXZILEVBQUFBLGtCQUFBQSxPQUFPUyxPQUFPLGNBQWRULHNDQUFBQSxnQkFBZ0JzQixNQUFNLEtBQUk7WUFDekNaLE9BQU9WLE9BQU9VLEtBQUs7WUFDbkJnRCxXQUFXLElBQUlzRCxPQUFPUSxXQUFXO1FBQ25DO1FBQ0E3RyxRQUFRQyxHQUFHLENBQUMsMkJBQWlCO1lBQzNCNkcsWUFBWSxPQUFPeEgsWUFBWTtZQUMvQnlILGVBQWUsT0FBT3hILGVBQWU7WUFDckN5SCxZQUFZLE9BQU94SCxZQUFZO1FBQ2pDO1FBRUEsSUFBSTtZQUNGUSxRQUFRQyxHQUFHLENBQUM7WUFDWixJQUFJLENBQUN3RyxjQUFjLENBQUNwSDtZQUNwQlcsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTSxJQUFJLENBQUNiLFdBQVcsQ0FBQ0MsUUFBUUMsU0FBU0MsWUFBWUM7WUFDcERRLFFBQVFDLEdBQUcsQ0FBQztZQUNaRCxRQUFROEIsUUFBUTtRQUNsQixFQUFFLE9BQU9FLE9BQU87WUFDZGhDLFFBQVFVLEtBQUssQ0FBQztZQUNkVixRQUFRZ0MsS0FBSyxDQUFDLGdDQUFzQkE7WUFDcENoQyxRQUFRZ0MsS0FBSyxDQUFDLHNCQUFZQSxpQkFBaUIrQyxRQUFRL0MsTUFBTWlGLFdBQVcsQ0FBQ3BGLElBQUksR0FBRyxPQUFPRztZQUNuRmhDLFFBQVE4QixRQUFRO1lBRWhCLElBQUlFLGlCQUFpQitDLE9BQU87Z0JBQzFCdkYsUUFBUXdDLE1BQU1sQyxPQUFPO1lBQ3ZCLE9BQU87Z0JBQ0xOLFFBQVE7WUFDVjtZQUNBUSxRQUFROEIsUUFBUTtRQUNsQjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNb0YsY0FBY3RILFFBQWdCLEVBQUVDLE1BQWMsRUFBRXNILFNBQWlCLEVBQW9CO1FBQ3pGLElBQUk7WUFDRixNQUFNbEQsV0FBVyxNQUFNL0IsTUFBTSxhQUF5QnJDLE9BQVpELFVBQVMsS0FBcUJ1SCxPQUFsQnRILFFBQU8sYUFBcUIsT0FBVnNILFlBQWE7Z0JBQ25GaEQsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFFQSxJQUFJLENBQUNILFNBQVM5QixFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSTRDLE1BQU0sNkJBQWlELE9BQXBCZCxTQUFTUSxVQUFVO1lBQ2xFO1lBRUEsT0FBTztRQUNULEVBQUUsT0FBT3pDLE9BQU87WUFDZGhDLFFBQVFnQyxLQUFLLENBQUMsNkJBQTZCQTtZQUMzQyxPQUFPO1FBQ1Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTW9GLGNBQWN4SCxRQUFnQixFQUFFQyxNQUFjLEVBQUVzSCxTQUFpQixFQUFFRSxVQUFrQixFQUFvQjtRQUM3RyxJQUFJO1lBQ0YsTUFBTXBELFdBQVcsTUFBTS9CLE1BQU0sYUFBeUJyQyxPQUFaRCxVQUFTLEtBQXFCdUgsT0FBbEJ0SCxRQUFPLGFBQXFCLE9BQVZzSCxZQUFhO2dCQUNuRmhELFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUFFMUQsU0FBU3dHO2dCQUFXO1lBQzdDO1lBRUEsSUFBSSxDQUFDcEQsU0FBUzlCLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJNEMsTUFBTSwrQkFBbUQsT0FBcEJkLFNBQVNRLFVBQVU7WUFDcEU7WUFFQSxPQUFPO1FBQ1QsRUFBRSxPQUFPekMsT0FBTztZQUNkaEMsUUFBUWdDLEtBQUssQ0FBQywrQkFBK0JBO1lBQzdDLE9BQU87UUFDVDtJQUNGOzthQWxZaUJrQyxjQUFjO2FBQ3ZCekUsa0JBQTBDOztBQWtZcEQ7QUFFQSwrQkFBK0I7QUFDeEIsTUFBTTZILFlBQVksSUFBSW5JLFlBQVk7QUFDekMsK0RBQWVtSSxTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9saWIvc2VydmljZXMvYWlTZXJ2aWNlLnRzPzY1OGUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gU2VydmnDp28gcGFyYSBpbnRlZ3Jhw6fDo28gY29tIEZpcmViYXNlIEZ1bmN0aW9ucyBkZSBJQVxuXG5leHBvcnQgaW50ZXJmYWNlIENoYXRNZXNzYWdlIHtcbiAgaWQ6IHN0cmluZztcbiAgY29udGVudDogc3RyaW5nO1xuICByb2xlOiAndXNlcicgfCAnYXNzaXN0YW50JztcbiAgdGltZXN0YW1wOiBzdHJpbmc7XG4gIGlzRmF2b3JpdGU/OiBib29sZWFuO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFN0cmVhbVJlc3BvbnNlIHtcbiAgY29udGVudDogc3RyaW5nO1xuICBpc0NvbXBsZXRlOiBib29sZWFuO1xuICBlcnJvcj86IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBBSVNlcnZpY2VDb25maWcge1xuICB1c2VybmFtZTogc3RyaW5nO1xuICBjaGF0SWQ6IHN0cmluZztcbiAgbWVzc2FnZTogc3RyaW5nO1xuICBtb2RlbD86IHN0cmluZztcbn1cblxuY2xhc3MgQUlTZXJ2aWNlIHtcbiAgcHJpdmF0ZSByZWFkb25seSBmdW5jdGlvblVybCA9ICdodHRwczovL3VzLWNlbnRyYWwxLXJhZnRob3ItMDAwMS5jbG91ZGZ1bmN0aW9ucy5uZXQvY2hhdFdpdGhBSSc7XG4gIHByaXZhdGUgYWJvcnRDb250cm9sbGVyOiBBYm9ydENvbnRyb2xsZXIgfCBudWxsID0gbnVsbDtcblxuICAvKipcbiAgICogRW52aWEgbWVuc2FnZW0gcGFyYSBhIElBIGNvbSBzdHJlYW1pbmdcbiAgICovXG4gIGFzeW5jIHNlbmRNZXNzYWdlKFxuICAgIGNvbmZpZzogQUlTZXJ2aWNlQ29uZmlnLFxuICAgIG9uQ2h1bms6IChjaHVuazogc3RyaW5nKSA9PiB2b2lkLFxuICAgIG9uQ29tcGxldGU6IChmdWxsUmVzcG9uc2U6IHN0cmluZykgPT4gdm9pZCxcbiAgICBvbkVycm9yOiAoZXJyb3I6IHN0cmluZykgPT4gdm9pZFxuICApOiBQcm9taXNlPHZvaWQ+IHtcbiAgICB0cnkge1xuICAgICAgLy8gQ3JpYXIgbm92byBBYm9ydENvbnRyb2xsZXIgcGFyYSBlc3RhIHJlcXVpc2nDp8Ojb1xuICAgICAgdGhpcy5hYm9ydENvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG5cbiAgICAgIC8vIFByZXBhcmFyIGRhZG9zIGRhIHJlcXVpc2nDp8Ojb1xuICAgICAgY29uc3QgcmVxdWVzdERhdGEgPSB7XG4gICAgICAgIHVzZXJuYW1lOiBjb25maWcudXNlcm5hbWUsXG4gICAgICAgIGNoYXRJZDogY29uZmlnLmNoYXRJZCxcbiAgICAgICAgbWVzc2FnZTogY29uZmlnLm1lc3NhZ2UsXG4gICAgICAgIG1vZGVsOiBjb25maWcubW9kZWwsXG4gICAgICB9O1xuXG4gICAgICAvLyBFbnZpYXIgcmVxdWlzacOnw6NvXG5cbiAgICAgIC8vIEJ1c2NhciBjb25maWd1cmHDp8O1ZXMgZG8gY2hhdCBkbyBGaXJlc3RvcmUgcGFyYSBtb3N0cmFyIG5vcyBsb2dzXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBCdXNjYW5kbyBjb25maWd1cmHDp8O1ZXMgZG8gY2hhdCBkbyBGaXJlc3RvcmUgcGFyYSBkZWJ1Zy4uLicpO1xuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gSW1wb3J0YXIgRmlyZWJhc2UgZGluYW1pY2FtZW50ZSBwYXJhIGV2aXRhciBwcm9ibGVtYXMgZGUgU1NSXG4gICAgICAgIGNvbnN0IHsgZG9jLCBnZXREb2MgfSA9IGF3YWl0IGltcG9ydCgnZmlyZWJhc2UvZmlyZXN0b3JlJyk7XG4gICAgICAgIGNvbnN0IHsgZGIgfSA9IGF3YWl0IGltcG9ydCgnQC9saWIvZmlyZWJhc2UnKTtcblxuICAgICAgICBjb25zdCBjaGF0RG9jUmVmID0gZG9jKGRiLCAndXN1YXJpb3MnLCByZXF1ZXN0RGF0YS51c2VybmFtZSwgJ2NvbnZlcnNhcycsIHJlcXVlc3REYXRhLmNoYXRJZCk7XG4gICAgICAgIGNvbnN0IGNoYXREb2NTbmFwID0gYXdhaXQgZ2V0RG9jKGNoYXREb2NSZWYpO1xuXG4gICAgICAgIGlmIChjaGF0RG9jU25hcC5leGlzdHMoKSkge1xuICAgICAgICAgIGNvbnN0IGNoYXRDb25maWcgPSBjaGF0RG9jU25hcC5kYXRhKCk7XG4gICAgICAgICAgY29uc29sZS5ncm91cCgn4pqZ77iPIENPTkZJR1VSQcOHw5VFUyBETyBDSEFUIChGSVJFU1RPUkUpJyk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfjq8gU3lzdGVtIFByb21wdDonLCB7XG4gICAgICAgICAgICBsZW5ndGg6IGNoYXRDb25maWcuc3lzdGVtUHJvbXB0Py5sZW5ndGggfHwgMCxcbiAgICAgICAgICAgIGNvbnRlbnQ6IGNoYXRDb25maWcuc3lzdGVtUHJvbXB0IHx8ICcodmF6aW8pJyxcbiAgICAgICAgICAgIHByZXZpZXc6IGNoYXRDb25maWcuc3lzdGVtUHJvbXB0Py5zdWJzdHJpbmcoMCwgMjAwKSArIChjaGF0Q29uZmlnLnN5c3RlbVByb21wdD8ubGVuZ3RoID4gMjAwID8gJy4uLicgOiAnJylcbiAgICAgICAgICB9KTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+TnSBDb250ZXh0bzonLCB7XG4gICAgICAgICAgICBsZW5ndGg6IGNoYXRDb25maWcuY29udGV4dD8ubGVuZ3RoIHx8IDAsXG4gICAgICAgICAgICBjb250ZW50OiBjaGF0Q29uZmlnLmNvbnRleHQgfHwgJyh2YXppbyknLFxuICAgICAgICAgICAgcHJldmlldzogY2hhdENvbmZpZy5jb250ZXh0Py5zdWJzdHJpbmcoMCwgMjAwKSArIChjaGF0Q29uZmlnLmNvbnRleHQ/Lmxlbmd0aCA+IDIwMCA/ICcuLi4nIDogJycpXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ/CfjKHvuI8gUGFyw6JtZXRyb3MgZGUgR2VyYcOnw6NvOicsIHtcbiAgICAgICAgICAgIHRlbXBlcmF0dXJlOiBjaGF0Q29uZmlnLnRlbXBlcmF0dXJlLFxuICAgICAgICAgICAgdGVtcGVyYXR1cmVUeXBlOiB0eXBlb2YgY2hhdENvbmZpZy50ZW1wZXJhdHVyZSxcbiAgICAgICAgICAgIGZyZXF1ZW5jeVBlbmFsdHk6IGNoYXRDb25maWcuZnJlcXVlbmN5UGVuYWx0eSxcbiAgICAgICAgICAgIGZyZXF1ZW5jeVBlbmFsdHlUeXBlOiB0eXBlb2YgY2hhdENvbmZpZy5mcmVxdWVuY3lQZW5hbHR5LFxuICAgICAgICAgICAgcmVwZXRpdGlvblBlbmFsdHk6IGNoYXRDb25maWcucmVwZXRpdGlvblBlbmFsdHksXG4gICAgICAgICAgICByZXBldGl0aW9uUGVuYWx0eVR5cGU6IHR5cGVvZiBjaGF0Q29uZmlnLnJlcGV0aXRpb25QZW5hbHR5LFxuICAgICAgICAgICAgbWF4VG9rZW5zOiBjaGF0Q29uZmlnLm1heFRva2VucyxcbiAgICAgICAgICAgIG1heFRva2Vuc1R5cGU6IHR5cGVvZiBjaGF0Q29uZmlnLm1heFRva2VucyxcbiAgICAgICAgICAgIGxhdGV4SW5zdHJ1Y3Rpb25zOiBjaGF0Q29uZmlnLmxhdGV4SW5zdHJ1Y3Rpb25zXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfk4ogTWV0YWRhZG9zOicsIHtcbiAgICAgICAgICAgIGxhc3RVc2VkTW9kZWw6IGNoYXRDb25maWcubGFzdFVzZWRNb2RlbCxcbiAgICAgICAgICAgIGNyZWF0ZWRBdDogY2hhdENvbmZpZy5jcmVhdGVkQXQsXG4gICAgICAgICAgICBsYXN0VXBkYXRlZEF0OiBjaGF0Q29uZmlnLmxhc3RVcGRhdGVkQXQsXG4gICAgICAgICAgICBuYW1lOiBjaGF0Q29uZmlnLm5hbWVcbiAgICAgICAgICB9KTtcbiAgICAgICAgICBjb25zb2xlLmdyb3VwRW5kKCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gRG9jdW1lbnRvIGRvIGNoYXQgbsOjbyBlbmNvbnRyYWRvIG5vIEZpcmVzdG9yZScpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBOw6NvIGZvaSBwb3Nzw612ZWwgYnVzY2FyIGNvbmZpZ3VyYcOnw7VlcyBkbyBjaGF0IGRvIEZpcmVzdG9yZTonLCBlcnJvcik7XG4gICAgICB9XG5cbiAgICAgIC8vIEJ1c2NhciBoaXN0w7NyaWNvIGRlIG1lbnNhZ2VucyBkbyBTdG9yYWdlIHBhcmEgbW9zdHJhciBub3MgbG9nc1xuICAgICAgY29uc29sZS5sb2coJ/CflI0gQnVzY2FuZG8gaGlzdMOzcmljbyBkZSBtZW5zYWdlbnMgZG8gU3RvcmFnZSBwYXJhIGRlYnVnLi4uJyk7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBtZXNzYWdlc1Jlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvY2hhdC8ke3JlcXVlc3REYXRhLnVzZXJuYW1lfS8ke3JlcXVlc3REYXRhLmNoYXRJZH1gKTtcbiAgICAgICAgaWYgKG1lc3NhZ2VzUmVzcG9uc2Uub2spIHtcbiAgICAgICAgICBjb25zdCBjaGF0RGF0YSA9IGF3YWl0IG1lc3NhZ2VzUmVzcG9uc2UuanNvbigpO1xuICAgICAgICAgIGNvbnN0IG1lc3NhZ2VzID0gY2hhdERhdGEubWVzc2FnZXMgfHwgW107XG4gICAgICAgICAgY29uc29sZS5ncm91cCgn8J+SrCBISVNUw5NSSUNPIERFIE1FTlNBR0VOUyAoU1RPUkFHRSknKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+TiiBUb3RhbCBkZSBtZW5zYWdlbnM6JywgbWVzc2FnZXMubGVuZ3RoKTtcblxuICAgICAgICAgIGlmIChtZXNzYWdlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TiyDDmmx0aW1hcyA1IG1lbnNhZ2VuczonLCBtZXNzYWdlcy5zbGljZSgtNSkubWFwKChtc2c6IGFueSwgaW5kZXg6IG51bWJlcikgPT4gKHtcbiAgICAgICAgICAgICAgaW5kZXg6IG1lc3NhZ2VzLmxlbmd0aCAtIDUgKyBpbmRleCxcbiAgICAgICAgICAgICAgaWQ6IG1zZy5pZCxcbiAgICAgICAgICAgICAgcm9sZTogbXNnLnJvbGUsXG4gICAgICAgICAgICAgIGNvbnRlbnRMZW5ndGg6IG1zZy5jb250ZW50Py5sZW5ndGggfHwgMCxcbiAgICAgICAgICAgICAgY29udGVudFByZXZpZXc6IG1zZy5jb250ZW50Py5zdWJzdHJpbmcoMCwgMTAwKSArIChtc2cuY29udGVudD8ubGVuZ3RoID4gMTAwID8gJy4uLicgOiAnJyksXG4gICAgICAgICAgICAgIHRpbWVzdGFtcDogbXNnLnRpbWVzdGFtcCxcbiAgICAgICAgICAgICAgaGFzQXR0YWNobWVudHM6ICEhKG1zZy5hdHRhY2htZW50cyAmJiBtc2cuYXR0YWNobWVudHMubGVuZ3RoID4gMCksXG4gICAgICAgICAgICAgIGF0dGFjaG1lbnRDb3VudDogbXNnLmF0dGFjaG1lbnRzPy5sZW5ndGggfHwgMFxuICAgICAgICAgICAgfSkpKTtcblxuICAgICAgICAgICAgaWYgKG1lc3NhZ2VzLmxlbmd0aCA+IDUpIHtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk5wgTWVuc2FnZW5zIG1haXMgYW50aWdhczonLCBgJHttZXNzYWdlcy5sZW5ndGggLSA1fSBtZW5zYWdlbnMgYW50ZXJpb3JlcyBuw6NvIG1vc3RyYWRhc2ApO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBFc3RhdMOtc3RpY2FzIGRvIGhpc3TDs3JpY29cbiAgICAgICAgICAgIGNvbnN0IHVzZXJNZXNzYWdlcyA9IG1lc3NhZ2VzLmZpbHRlcigobXNnOiBhbnkpID0+IG1zZy5yb2xlID09PSAndXNlcicpLmxlbmd0aDtcbiAgICAgICAgICAgIGNvbnN0IGFzc2lzdGFudE1lc3NhZ2VzID0gbWVzc2FnZXMuZmlsdGVyKChtc2c6IGFueSkgPT4gbXNnLnJvbGUgPT09ICdhc3Npc3RhbnQnKS5sZW5ndGg7XG4gICAgICAgICAgICBjb25zdCB0b3RhbENoYXJzID0gbWVzc2FnZXMucmVkdWNlKChhY2M6IG51bWJlciwgbXNnOiBhbnkpID0+IGFjYyArIChtc2cuY29udGVudD8ubGVuZ3RoIHx8IDApLCAwKTtcblxuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk4ggRXN0YXTDrXN0aWNhcyBkbyBIaXN0w7NyaWNvOicsIHtcbiAgICAgICAgICAgICAgdG90YWxNZXNzYWdlczogbWVzc2FnZXMubGVuZ3RoLFxuICAgICAgICAgICAgICB1c2VyTWVzc2FnZXMsXG4gICAgICAgICAgICAgIGFzc2lzdGFudE1lc3NhZ2VzLFxuICAgICAgICAgICAgICB0b3RhbENoYXJhY3RlcnM6IHRvdGFsQ2hhcnMsXG4gICAgICAgICAgICAgIGF2ZXJhZ2VNZXNzYWdlTGVuZ3RoOiBtZXNzYWdlcy5sZW5ndGggPiAwID8gTWF0aC5yb3VuZCh0b3RhbENoYXJzIC8gbWVzc2FnZXMubGVuZ3RoKSA6IDAsXG4gICAgICAgICAgICAgIG9sZGVzdE1lc3NhZ2U6IG1lc3NhZ2VzWzBdPy50aW1lc3RhbXAsXG4gICAgICAgICAgICAgIG5ld2VzdE1lc3NhZ2U6IG1lc3NhZ2VzW21lc3NhZ2VzLmxlbmd0aCAtIDFdPy50aW1lc3RhbXBcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TrSBOZW5odW1hIG1lbnNhZ2VtIGVuY29udHJhZGEgbm8gaGlzdMOzcmljbycpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnNvbGUuZ3JvdXBFbmQoKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBGYWxoYSBhbyBidXNjYXIgbWVuc2FnZW5zIC0gU3RhdHVzOicsIG1lc3NhZ2VzUmVzcG9uc2Uuc3RhdHVzKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gTsOjbyBmb2kgcG9zc8OtdmVsIGJ1c2NhciBoaXN0w7NyaWNvIGRlIG1lbnNhZ2VuczonLCBlcnJvcik7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUuZ3JvdXBFbmQoKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCh0aGlzLmZ1bmN0aW9uVXJsLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocmVxdWVzdERhdGEpLFxuICAgICAgICBzaWduYWw6IHRoaXMuYWJvcnRDb250cm9sbGVyLnNpZ25hbCxcbiAgICAgIH0pO1xuXG4gICAgICAvLyBERUJVRzogTG9nIGRhIHJlc3Bvc3RhXG4gICAgICBjb25zb2xlLmdyb3VwKCfwn5OhIEFJIFNFUlZJQ0UgLSBSRVNQT1NUQSBSRUNFQklEQScpO1xuICAgICAgY29uc29sZS5sb2coJ+KchSBTdGF0dXM6JywgcmVzcG9uc2Uuc3RhdHVzLCByZXNwb25zZS5zdGF0dXNUZXh0KTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OLIEhlYWRlcnM6JywgT2JqZWN0LmZyb21FbnRyaWVzKHJlc3BvbnNlLmhlYWRlcnMuZW50cmllcygpKSk7XG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBSZXNwb25zZSBPSzonLCByZXNwb25zZS5vayk7XG4gICAgICBjb25zb2xlLmxvZygn8J+MkCBVUkw6JywgcmVzcG9uc2UudXJsKTtcbiAgICAgIGNvbnNvbGUuZ3JvdXBFbmQoKTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGNvbnNvbGUuZ3JvdXAoJ+KdjCBBSSBTRVJWSUNFIC0gRVJSTyBOQSBSRVNQT1NUQScpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCdTdGF0dXM6JywgcmVzcG9uc2Uuc3RhdHVzKTtcbiAgICAgICAgY29uc29sZS5lcnJvcignU3RhdHVzIFRleHQ6JywgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIERhdGE6JywgZXJyb3JEYXRhKTtcbiAgICAgICAgY29uc29sZS5ncm91cEVuZCgpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLmVycm9yIHx8IGBIVFRQICR7cmVzcG9uc2Uuc3RhdHVzfTogJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApO1xuICAgICAgfVxuXG4gICAgICBpZiAoIXJlc3BvbnNlLmJvZHkpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdSZXNwb25zZSBib2R5IGlzIG5vdCBhdmFpbGFibGUnKTtcbiAgICAgIH1cblxuICAgICAgLy8gUHJvY2Vzc2FyIHN0cmVhbVxuICAgICAgY29uc3QgcmVhZGVyID0gcmVzcG9uc2UuYm9keS5nZXRSZWFkZXIoKTtcbiAgICAgIGNvbnN0IGRlY29kZXIgPSBuZXcgVGV4dERlY29kZXIoKTtcbiAgICAgIGxldCBmdWxsUmVzcG9uc2UgPSAnJztcblxuICAgICAgLy8gUHJvY2Vzc2FyIHN0cmVhbVxuXG4gICAgICB0cnkge1xuICAgICAgICB3aGlsZSAodHJ1ZSkge1xuICAgICAgICAgIGNvbnN0IHsgZG9uZSwgdmFsdWUgfSA9IGF3YWl0IHJlYWRlci5yZWFkKCk7XG5cbiAgICAgICAgICBpZiAoZG9uZSkge1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgY29uc3QgY2h1bmsgPSBkZWNvZGVyLmRlY29kZSh2YWx1ZSwgeyBzdHJlYW06IHRydWUgfSk7XG5cbiAgICAgICAgICBmdWxsUmVzcG9uc2UgKz0gY2h1bms7XG5cbiAgICAgICAgICAvLyBDaGFtYXIgY2FsbGJhY2sgcGFyYSBjYWRhIGNodW5rXG4gICAgICAgICAgb25DaHVuayhjaHVuayk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBDaGFtYXIgY2FsbGJhY2sgZGUgY29uY2x1c8Ojb1xuICAgICAgICBvbkNvbXBsZXRlKGZ1bGxSZXNwb25zZSk7XG5cbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHJlYWRlci5yZWxlYXNlTG9jaygpO1xuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICAgIGlmIChlcnJvci5uYW1lID09PSAnQWJvcnRFcnJvcicpIHtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgb25FcnJvcihlcnJvci5tZXNzYWdlKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG9uRXJyb3IoJ0Vycm8gZGVzY29uaGVjaWRvIG5hIGNvbXVuaWNhw6fDo28gY29tIGEgSUEnKTtcbiAgICAgIH1cbiAgICB9IGZpbmFsbHkge1xuICAgICAgdGhpcy5hYm9ydENvbnRyb2xsZXIgPSBudWxsO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDYW5jZWxhIGEgcmVxdWlzacOnw6NvIGVtIGFuZGFtZW50b1xuICAgKi9cbiAgY2FuY2VsUmVxdWVzdCgpOiB2b2lkIHtcbiAgICBpZiAodGhpcy5hYm9ydENvbnRyb2xsZXIpIHtcbiAgICAgIHRoaXMuYWJvcnRDb250cm9sbGVyLmFib3J0KCk7XG4gICAgICB0aGlzLmFib3J0Q29udHJvbGxlciA9IG51bGw7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFZlcmlmaWNhIHNlIGjDoSB1bWEgcmVxdWlzacOnw6NvIGVtIGFuZGFtZW50b1xuICAgKi9cbiAgaXNSZXF1ZXN0SW5Qcm9ncmVzcygpOiBib29sZWFuIHtcbiAgICByZXR1cm4gdGhpcy5hYm9ydENvbnRyb2xsZXIgIT09IG51bGw7XG4gIH1cblxuICAvKipcbiAgICogQ2FycmVnYSBtZW5zYWdlbnMgZGUgdW0gY2hhdCB1c2FuZG8gYSBBUEkgcm91dGVcbiAgICovXG4gIGFzeW5jIGxvYWRDaGF0TWVzc2FnZXModXNlcm5hbWU6IHN0cmluZywgY2hhdElkOiBzdHJpbmcpOiBQcm9taXNlPENoYXRNZXNzYWdlW10+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9jaGF0LyR7dXNlcm5hbWV9LyR7Y2hhdElkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEVycm8gYW8gY2FycmVnYXIgY2hhdDogJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBjaGF0RGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIHJldHVybiBjaGF0RGF0YS5tZXNzYWdlcyB8fCBbXTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGNhcnJlZ2FyIG1lbnNhZ2VucyBkbyBjaGF0OicsIGVycm9yKTtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ29udmVydGUgbWVuc2FnZW5zIGRvIGZvcm1hdG8gaW50ZXJubyBwYXJhIG8gZm9ybWF0byBkYSBJQVxuICAgKi9cbiAgY29udmVydFRvQUlGb3JtYXQobWVzc2FnZXM6IGFueVtdKTogQ2hhdE1lc3NhZ2VbXSB7XG4gICAgcmV0dXJuIG1lc3NhZ2VzLm1hcChtc2cgPT4gKHtcbiAgICAgIGlkOiBtc2cuaWQsXG4gICAgICBjb250ZW50OiBtc2cuY29udGVudCxcbiAgICAgIHJvbGU6IG1zZy5zZW5kZXIgPT09ICd1c2VyJyA/ICd1c2VyJyA6ICdhc3Npc3RhbnQnLFxuICAgICAgdGltZXN0YW1wOiBtc2cudGltZXN0YW1wLFxuICAgICAgaXNGYXZvcml0ZTogbXNnLmlzRmF2b3JpdGUgfHwgZmFsc2UsXG4gICAgfSkpO1xuICB9XG5cbiAgLyoqXG4gICAqIENvbnZlcnRlIG1lbnNhZ2VucyBkbyBmb3JtYXRvIGRhIElBIHBhcmEgbyBmb3JtYXRvIGludGVybm9cbiAgICovXG4gIGNvbnZlcnRGcm9tQUlGb3JtYXQobWVzc2FnZXM6IENoYXRNZXNzYWdlW10pOiBhbnlbXSB7XG4gICAgcmV0dXJuIG1lc3NhZ2VzLm1hcChtc2cgPT4gKHtcbiAgICAgIGlkOiBtc2cuaWQsXG4gICAgICBjb250ZW50OiBtc2cuY29udGVudCxcbiAgICAgIHNlbmRlcjogbXNnLnJvbGUgPT09ICd1c2VyJyA/ICd1c2VyJyA6ICdhaScsXG4gICAgICB0aW1lc3RhbXA6IG1zZy50aW1lc3RhbXAsXG4gICAgICBpc0Zhdm9yaXRlOiBtc2cuaXNGYXZvcml0ZSB8fCBmYWxzZSxcbiAgICB9KSk7XG4gIH1cblxuICAvKipcbiAgICogR2VyYSBJRCDDum5pY28gcGFyYSBtZW5zYWdlbnNcbiAgICovXG4gIGdlbmVyYXRlTWVzc2FnZUlkKCk6IHN0cmluZyB7XG4gICAgcmV0dXJuIERhdGUubm93KCkudG9TdHJpbmcoMzYpICsgTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyaW5nKDIpO1xuICB9XG5cbiAgLyoqXG4gICAqIFZhbGlkYSBjb25maWd1cmHDp8OjbyBhbnRlcyBkZSBlbnZpYXJcbiAgICovXG4gIHByaXZhdGUgdmFsaWRhdGVDb25maWcoY29uZmlnOiBBSVNlcnZpY2VDb25maWcpOiB2b2lkIHtcbiAgICBpZiAoIWNvbmZpZy51c2VybmFtZT8udHJpbSgpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXJuYW1lIMOpIG9icmlnYXTDs3JpbycpO1xuICAgIH1cbiAgICBpZiAoIWNvbmZpZy5jaGF0SWQ/LnRyaW0oKSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdDaGF0IElEIMOpIG9icmlnYXTDs3JpbycpO1xuICAgIH1cbiAgICBpZiAoIWNvbmZpZy5tZXNzYWdlPy50cmltKCkpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTWVuc2FnZW0gw6kgb2JyaWdhdMOzcmlhJyk7XG4gICAgfVxuICAgIGlmIChjb25maWcubWVzc2FnZS5sZW5ndGggPiAxMDAwMCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdNZW5zYWdlbSBtdWl0byBsb25nYSAobcOheGltbyAxMC4wMDAgY2FyYWN0ZXJlcyknKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogRW52aWEgbWVuc2FnZW0gY29tIHZhbGlkYcOnw6NvXG4gICAqL1xuICBhc3luYyBzZW5kTWVzc2FnZVNhZmUoXG4gICAgY29uZmlnOiBBSVNlcnZpY2VDb25maWcsXG4gICAgb25DaHVuazogKGNodW5rOiBzdHJpbmcpID0+IHZvaWQsXG4gICAgb25Db21wbGV0ZTogKGZ1bGxSZXNwb25zZTogc3RyaW5nKSA9PiB2b2lkLFxuICAgIG9uRXJyb3I6IChlcnJvcjogc3RyaW5nKSA9PiB2b2lkXG4gICk6IFByb21pc2U8dm9pZD4ge1xuICAgIGNvbnNvbGUuZ3JvdXAoJ/CfmoAgQUkgU0VSVklDRSAtIFNFTkQgTUVTU0FHRSBTQUZFJyk7XG4gICAgY29uc29sZS5sb2coJ/CflKcgQ29uZmlnIHJlY2ViaWRhOicsIHtcbiAgICAgIHVzZXJuYW1lOiBjb25maWcudXNlcm5hbWUsXG4gICAgICBjaGF0SWQ6IGNvbmZpZy5jaGF0SWQsXG4gICAgICBtZXNzYWdlTGVuZ3RoOiBjb25maWcubWVzc2FnZT8ubGVuZ3RoIHx8IDAsXG4gICAgICBtb2RlbDogY29uZmlnLm1vZGVsLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9KTtcbiAgICBjb25zb2xlLmxvZygn8J+TiyBDYWxsYmFja3M6Jywge1xuICAgICAgaGFzT25DaHVuazogdHlwZW9mIG9uQ2h1bmsgPT09ICdmdW5jdGlvbicsXG4gICAgICBoYXNPbkNvbXBsZXRlOiB0eXBlb2Ygb25Db21wbGV0ZSA9PT0gJ2Z1bmN0aW9uJyxcbiAgICAgIGhhc09uRXJyb3I6IHR5cGVvZiBvbkVycm9yID09PSAnZnVuY3Rpb24nXG4gICAgfSk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ+KchSBWYWxpZGFuZG8gY29uZmlndXJhw6fDo28uLi4nKTtcbiAgICAgIHRoaXMudmFsaWRhdGVDb25maWcoY29uZmlnKTtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgQ29uZmlndXJhw6fDo28gdsOhbGlkYSwgZW52aWFuZG8gbWVuc2FnZW0uLi4nKTtcbiAgICAgIGF3YWl0IHRoaXMuc2VuZE1lc3NhZ2UoY29uZmlnLCBvbkNodW5rLCBvbkNvbXBsZXRlLCBvbkVycm9yKTtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgTWVuc2FnZW0gZW52aWFkYSBjb20gc3VjZXNzbycpO1xuICAgICAgY29uc29sZS5ncm91cEVuZCgpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmdyb3VwKCfinYwgRVJSTyBFTSBTRU5EIE1FU1NBR0UgU0FGRScpO1xuICAgICAgY29uc29sZS5lcnJvcign8J+aqCBFcnJvIGNhcHR1cmFkbzonLCBlcnJvcik7XG4gICAgICBjb25zb2xlLmVycm9yKCfwn5OdIFRpcG86JywgZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLmNvbnN0cnVjdG9yLm5hbWUgOiB0eXBlb2YgZXJyb3IpO1xuICAgICAgY29uc29sZS5ncm91cEVuZCgpO1xuXG4gICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICBvbkVycm9yKGVycm9yLm1lc3NhZ2UpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgb25FcnJvcignRXJybyBkZSB2YWxpZGHDp8OjbycpO1xuICAgICAgfVxuICAgICAgY29uc29sZS5ncm91cEVuZCgpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBEZWxldGEgdW1hIG1lbnNhZ2VtIGRvIGNoYXQgbm8gRmlyZWJhc2UgU3RvcmFnZVxuICAgKi9cbiAgYXN5bmMgZGVsZXRlTWVzc2FnZSh1c2VybmFtZTogc3RyaW5nLCBjaGF0SWQ6IHN0cmluZywgbWVzc2FnZUlkOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9jaGF0LyR7dXNlcm5hbWV9LyR7Y2hhdElkfS9tZXNzYWdlLyR7bWVzc2FnZUlkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEVycm8gYW8gZGVsZXRhciBtZW5zYWdlbTogJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBkZWxldGFyIG1lbnNhZ2VtOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQXR1YWxpemEgdW1hIG1lbnNhZ2VtIG5vIGNoYXQgbm8gRmlyZWJhc2UgU3RvcmFnZVxuICAgKi9cbiAgYXN5bmMgdXBkYXRlTWVzc2FnZSh1c2VybmFtZTogc3RyaW5nLCBjaGF0SWQ6IHN0cmluZywgbWVzc2FnZUlkOiBzdHJpbmcsIG5ld0NvbnRlbnQ6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL2NoYXQvJHt1c2VybmFtZX0vJHtjaGF0SWR9L21lc3NhZ2UvJHttZXNzYWdlSWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdQVVQnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBjb250ZW50OiBuZXdDb250ZW50IH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBFcnJvIGFvIGF0dWFsaXphciBtZW5zYWdlbTogJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBhdHVhbGl6YXIgbWVuc2FnZW06JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxufVxuXG4vLyBFeHBvcnRhciBpbnN0w6JuY2lhIHNpbmdsZXRvblxuZXhwb3J0IGNvbnN0IGFpU2VydmljZSA9IG5ldyBBSVNlcnZpY2UoKTtcbmV4cG9ydCBkZWZhdWx0IGFpU2VydmljZTtcbiJdLCJuYW1lcyI6WyJBSVNlcnZpY2UiLCJzZW5kTWVzc2FnZSIsImNvbmZpZyIsIm9uQ2h1bmsiLCJvbkNvbXBsZXRlIiwib25FcnJvciIsImFib3J0Q29udHJvbGxlciIsIkFib3J0Q29udHJvbGxlciIsInJlcXVlc3REYXRhIiwidXNlcm5hbWUiLCJjaGF0SWQiLCJtZXNzYWdlIiwibW9kZWwiLCJjb25zb2xlIiwibG9nIiwiZG9jIiwiZ2V0RG9jIiwiZGIiLCJjaGF0RG9jUmVmIiwiY2hhdERvY1NuYXAiLCJleGlzdHMiLCJjaGF0Q29uZmlnIiwiZGF0YSIsImdyb3VwIiwibGVuZ3RoIiwic3lzdGVtUHJvbXB0IiwiY29udGVudCIsInByZXZpZXciLCJzdWJzdHJpbmciLCJjb250ZXh0IiwidGVtcGVyYXR1cmUiLCJ0ZW1wZXJhdHVyZVR5cGUiLCJmcmVxdWVuY3lQZW5hbHR5IiwiZnJlcXVlbmN5UGVuYWx0eVR5cGUiLCJyZXBldGl0aW9uUGVuYWx0eSIsInJlcGV0aXRpb25QZW5hbHR5VHlwZSIsIm1heFRva2VucyIsIm1heFRva2Vuc1R5cGUiLCJsYXRleEluc3RydWN0aW9ucyIsImxhc3RVc2VkTW9kZWwiLCJjcmVhdGVkQXQiLCJsYXN0VXBkYXRlZEF0IiwibmFtZSIsImdyb3VwRW5kIiwid2FybiIsImVycm9yIiwibWVzc2FnZXNSZXNwb25zZSIsImZldGNoIiwib2siLCJjaGF0RGF0YSIsImpzb24iLCJtZXNzYWdlcyIsInNsaWNlIiwibWFwIiwibXNnIiwiaW5kZXgiLCJpZCIsInJvbGUiLCJjb250ZW50TGVuZ3RoIiwiY29udGVudFByZXZpZXciLCJ0aW1lc3RhbXAiLCJoYXNBdHRhY2htZW50cyIsImF0dGFjaG1lbnRzIiwiYXR0YWNobWVudENvdW50IiwidXNlck1lc3NhZ2VzIiwiZmlsdGVyIiwiYXNzaXN0YW50TWVzc2FnZXMiLCJ0b3RhbENoYXJzIiwicmVkdWNlIiwiYWNjIiwidG90YWxNZXNzYWdlcyIsInRvdGFsQ2hhcmFjdGVycyIsImF2ZXJhZ2VNZXNzYWdlTGVuZ3RoIiwiTWF0aCIsInJvdW5kIiwib2xkZXN0TWVzc2FnZSIsIm5ld2VzdE1lc3NhZ2UiLCJzdGF0dXMiLCJyZXNwb25zZSIsImZ1bmN0aW9uVXJsIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5Iiwic2lnbmFsIiwic3RhdHVzVGV4dCIsIk9iamVjdCIsImZyb21FbnRyaWVzIiwiZW50cmllcyIsInVybCIsImVycm9yRGF0YSIsIkVycm9yIiwicmVhZGVyIiwiZ2V0UmVhZGVyIiwiZGVjb2RlciIsIlRleHREZWNvZGVyIiwiZnVsbFJlc3BvbnNlIiwiZG9uZSIsInZhbHVlIiwicmVhZCIsImNodW5rIiwiZGVjb2RlIiwic3RyZWFtIiwicmVsZWFzZUxvY2siLCJjYW5jZWxSZXF1ZXN0IiwiYWJvcnQiLCJpc1JlcXVlc3RJblByb2dyZXNzIiwibG9hZENoYXRNZXNzYWdlcyIsImNvbnZlcnRUb0FJRm9ybWF0Iiwic2VuZGVyIiwiaXNGYXZvcml0ZSIsImNvbnZlcnRGcm9tQUlGb3JtYXQiLCJnZW5lcmF0ZU1lc3NhZ2VJZCIsIkRhdGUiLCJub3ciLCJ0b1N0cmluZyIsInJhbmRvbSIsInZhbGlkYXRlQ29uZmlnIiwidHJpbSIsInNlbmRNZXNzYWdlU2FmZSIsIm1lc3NhZ2VMZW5ndGgiLCJ0b0lTT1N0cmluZyIsImhhc09uQ2h1bmsiLCJoYXNPbkNvbXBsZXRlIiwiaGFzT25FcnJvciIsImNvbnN0cnVjdG9yIiwiZGVsZXRlTWVzc2FnZSIsIm1lc3NhZ2VJZCIsInVwZGF0ZU1lc3NhZ2UiLCJuZXdDb250ZW50IiwiYWlTZXJ2aWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});