"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n// Serviço para integração com Firebase Functions de IA\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            var _requestData_message, _requestData_message1, _requestData_message2;\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model\n            };\n            // DEBUG: Log detalhado no console do navegador\n            console.group(\"\\uD83E\\uDD16 AI SERVICE - ENVIANDO REQUISI\\xc7\\xc3O\");\n            console.log(\"\\uD83D\\uDCE1 URL da Function:\", this.functionUrl);\n            console.log(\"\\uD83D\\uDCCB Dados da Requisi\\xe7\\xe3o:\", {\n                username: requestData.username,\n                chatId: requestData.chatId,\n                message: requestData.message,\n                messageLength: ((_requestData_message = requestData.message) === null || _requestData_message === void 0 ? void 0 : _requestData_message.length) || 0,\n                messagePreview: ((_requestData_message1 = requestData.message) === null || _requestData_message1 === void 0 ? void 0 : _requestData_message1.substring(0, 100)) + (((_requestData_message2 = requestData.message) === null || _requestData_message2 === void 0 ? void 0 : _requestData_message2.length) > 100 ? \"...\" : \"\"),\n                model: requestData.model,\n                timestamp: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDD27 Headers:\", {\n                \"Content-Type\": \"application/json\",\n                \"Method\": \"POST\"\n            });\n            console.log(\"\\uD83D\\uDCE6 Body JSON:\", JSON.stringify(requestData, null, 2));\n            console.log(\"\\uD83D\\uDCCA Estat\\xedsticas:\", {\n                bodySize: JSON.stringify(requestData).length + \" bytes\",\n                hasAbortController: !!this.abortController,\n                userAgent: navigator.userAgent\n            });\n            // Buscar configurações do chat do Firestore para mostrar nos logs\n            console.log(\"\\uD83D\\uDD0D Buscando configura\\xe7\\xf5es do chat do Firestore para debug...\");\n            try {\n                // Importar Firebase dinamicamente para evitar problemas de SSR\n                const { doc, getDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                const { db } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\"));\n                const chatDocRef = doc(db, \"usuarios\", requestData.username, \"conversas\", requestData.chatId);\n                const chatDocSnap = await getDoc(chatDocRef);\n                if (chatDocSnap.exists()) {\n                    var _chatConfig_systemPrompt, _chatConfig_systemPrompt1, _chatConfig_systemPrompt2, _chatConfig_context, _chatConfig_context1, _chatConfig_context2;\n                    const chatConfig = chatDocSnap.data();\n                    console.group(\"⚙️ CONFIGURA\\xc7\\xd5ES DO CHAT (FIRESTORE)\");\n                    console.log(\"\\uD83C\\uDFAF System Prompt:\", {\n                        length: ((_chatConfig_systemPrompt = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt === void 0 ? void 0 : _chatConfig_systemPrompt.length) || 0,\n                        content: chatConfig.systemPrompt || \"(vazio)\",\n                        preview: ((_chatConfig_systemPrompt1 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt1 === void 0 ? void 0 : _chatConfig_systemPrompt1.substring(0, 200)) + (((_chatConfig_systemPrompt2 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt2 === void 0 ? void 0 : _chatConfig_systemPrompt2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83D\\uDCDD Contexto:\", {\n                        length: ((_chatConfig_context = chatConfig.context) === null || _chatConfig_context === void 0 ? void 0 : _chatConfig_context.length) || 0,\n                        content: chatConfig.context || \"(vazio)\",\n                        preview: ((_chatConfig_context1 = chatConfig.context) === null || _chatConfig_context1 === void 0 ? void 0 : _chatConfig_context1.substring(0, 200)) + (((_chatConfig_context2 = chatConfig.context) === null || _chatConfig_context2 === void 0 ? void 0 : _chatConfig_context2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83C\\uDF21️ Par\\xe2metros de Gera\\xe7\\xe3o:\", {\n                        temperature: chatConfig.temperature,\n                        temperatureType: typeof chatConfig.temperature,\n                        frequencyPenalty: chatConfig.frequencyPenalty,\n                        frequencyPenaltyType: typeof chatConfig.frequencyPenalty,\n                        repetitionPenalty: chatConfig.repetitionPenalty,\n                        repetitionPenaltyType: typeof chatConfig.repetitionPenalty,\n                        maxTokens: chatConfig.maxTokens,\n                        maxTokensType: typeof chatConfig.maxTokens,\n                        latexInstructions: chatConfig.latexInstructions\n                    });\n                    console.log(\"\\uD83D\\uDCCA Metadados:\", {\n                        lastUsedModel: chatConfig.lastUsedModel,\n                        createdAt: chatConfig.createdAt,\n                        lastUpdatedAt: chatConfig.lastUpdatedAt,\n                        name: chatConfig.name\n                    });\n                    console.groupEnd();\n                } else {\n                    console.warn(\"⚠️ Documento do chat n\\xe3o encontrado no Firestore\");\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar configura\\xe7\\xf5es do chat do Firestore:\", error);\n            }\n            // Buscar histórico de mensagens do Storage para mostrar nos logs\n            console.log(\"\\uD83D\\uDD0D Buscando hist\\xf3rico de mensagens do Storage para debug...\");\n            try {\n                const messagesResponse = await fetch(\"/api/chat/\".concat(requestData.username, \"/\").concat(requestData.chatId));\n                if (messagesResponse.ok) {\n                    const chatData = await messagesResponse.json();\n                    const messages = chatData.messages || [];\n                    console.group(\"\\uD83D\\uDCAC HIST\\xd3RICO DE MENSAGENS (STORAGE)\");\n                    console.log(\"\\uD83D\\uDCCA Total de mensagens:\", messages.length);\n                    if (messages.length > 0) {\n                        var _messages_, _messages_1;\n                        console.log(\"\\uD83D\\uDCCB \\xdaltimas 5 mensagens:\", messages.slice(-5).map((msg, index)=>{\n                            var _msg_content, _msg_content1, _msg_content2, _msg_attachments;\n                            return {\n                                index: messages.length - 5 + index,\n                                id: msg.id,\n                                role: msg.role,\n                                contentLength: ((_msg_content = msg.content) === null || _msg_content === void 0 ? void 0 : _msg_content.length) || 0,\n                                contentPreview: ((_msg_content1 = msg.content) === null || _msg_content1 === void 0 ? void 0 : _msg_content1.substring(0, 100)) + (((_msg_content2 = msg.content) === null || _msg_content2 === void 0 ? void 0 : _msg_content2.length) > 100 ? \"...\" : \"\"),\n                                timestamp: msg.timestamp,\n                                hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                                attachmentCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0\n                            };\n                        }));\n                        if (messages.length > 5) {\n                            console.log(\"\\uD83D\\uDCDC Mensagens mais antigas:\", \"\".concat(messages.length - 5, \" mensagens anteriores n\\xe3o mostradas\"));\n                        }\n                        // Estatísticas do histórico\n                        const userMessages = messages.filter((msg)=>msg.role === \"user\").length;\n                        const assistantMessages = messages.filter((msg)=>msg.role === \"assistant\").length;\n                        const totalChars = messages.reduce((acc, msg)=>{\n                            var _msg_content;\n                            return acc + (((_msg_content = msg.content) === null || _msg_content === void 0 ? void 0 : _msg_content.length) || 0);\n                        }, 0);\n                        console.log(\"\\uD83D\\uDCC8 Estat\\xedsticas do Hist\\xf3rico:\", {\n                            totalMessages: messages.length,\n                            userMessages,\n                            assistantMessages,\n                            totalCharacters: totalChars,\n                            averageMessageLength: messages.length > 0 ? Math.round(totalChars / messages.length) : 0,\n                            oldestMessage: (_messages_ = messages[0]) === null || _messages_ === void 0 ? void 0 : _messages_.timestamp,\n                            newestMessage: (_messages_1 = messages[messages.length - 1]) === null || _messages_1 === void 0 ? void 0 : _messages_1.timestamp\n                        });\n                    } else {\n                        console.log(\"\\uD83D\\uDCED Nenhuma mensagem encontrada no hist\\xf3rico\");\n                    }\n                    console.groupEnd();\n                } else {\n                    console.warn(\"⚠️ Falha ao buscar mensagens - Status:\", messagesResponse.status);\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar hist\\xf3rico de mensagens:\", error);\n            }\n            console.groupEnd();\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            // DEBUG: Log da resposta\n            console.group(\"\\uD83D\\uDCE1 AI SERVICE - RESPOSTA RECEBIDA\");\n            console.log(\"✅ Status:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCCB Headers:\", Object.fromEntries(response.headers.entries()));\n            console.log(\"\\uD83D\\uDD0D Response OK:\", response.ok);\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            // Processar stream\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            console.group(\"❌ AI SERVICE - ERRO CAPTURADO\");\n            console.error(\"\\uD83D\\uDEA8 Tipo do erro:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.error(\"\\uD83D\\uDCDD Mensagem:\", error instanceof Error ? error.message : String(error));\n            console.error(\"\\uD83D\\uDD0D Stack trace:\", error instanceof Error ? error.stack : \"N/A\");\n            console.error(\"⏰ Timestamp:\", new Date().toISOString());\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    console.log(\"\\uD83D\\uDED1 Requisi\\xe7\\xe3o cancelada pelo usu\\xe1rio\");\n                    console.groupEnd();\n                    return;\n                }\n                console.error(\"\\uD83D\\uDD27 Detalhes completos:\", error);\n                console.groupEnd();\n                onError(error.message);\n            } else {\n                console.error(\"\\uD83D\\uDD27 Detalhes completos:\", error);\n                console.groupEnd();\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        var _config_message;\n        console.group(\"\\uD83D\\uDE80 AI SERVICE - SEND MESSAGE SAFE\");\n        console.log(\"\\uD83D\\uDD27 Config recebida:\", {\n            username: config.username,\n            chatId: config.chatId,\n            messageLength: ((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.length) || 0,\n            model: config.model,\n            timestamp: new Date().toISOString()\n        });\n        console.log(\"\\uD83D\\uDCCB Callbacks:\", {\n            hasOnChunk: typeof onChunk === \"function\",\n            hasOnComplete: typeof onComplete === \"function\",\n            hasOnError: typeof onError === \"function\"\n        });\n        try {\n            console.log(\"✅ Validando configura\\xe7\\xe3o...\");\n            this.validateConfig(config);\n            console.log(\"✅ Configura\\xe7\\xe3o v\\xe1lida, enviando mensagem...\");\n            await this.sendMessage(config, onChunk, onComplete, onError);\n            console.log(\"✅ Mensagem enviada com sucesso\");\n            console.groupEnd();\n        } catch (error) {\n            console.group(\"❌ ERRO EM SEND MESSAGE SAFE\");\n            console.error(\"\\uD83D\\uDEA8 Erro capturado:\", error);\n            console.error(\"\\uD83D\\uDCDD Tipo:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.groupEnd();\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n            console.groupEnd();\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});