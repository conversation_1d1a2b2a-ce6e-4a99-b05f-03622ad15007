"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n// Serviço para integração com Firebase Functions de IA\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model\n            };\n            // Enviar requisição\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            // Processar stream\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    return;\n                }\n                onError(error.message);\n            } else {\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        try {\n            this.validateConfig(config);\n            await this.sendMessage(config, onChunk, onComplete, onError);\n        } catch (error) {\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});