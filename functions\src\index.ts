import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import fetch from "node-fetch";

// Inicializar Firebase Admin
admin.initializeApp();

// Interfaces para tipagem
interface ChatMessage {
  id: string;
  content: string;
  role: "user" | "assistant";
  timestamp: string;
  isFavorite?: boolean;
}

interface ChatData {
  id: string;
  name: string;
  messages: ChatMessage[];
  createdAt: string;
  lastUpdated: string;
}

interface ChatConfig {
  systemPrompt: string;
  context: string;
  temperature: number;
  frequencyPenalty: number;
  repetitionPenalty: number;
  maxTokens: number;
  lastUsedModel: string;
  latexInstructions: boolean;
}

interface AIEndpoint {
  nome: string;
  url: string;
  apiKey: string;
  modeloPadrao: string;
  ativo: boolean;
}

interface UserSettings {
  endpoints: Record<string, AIEndpoint>;
  aparencia: {
    fonte: string;
    tamanhoFonte: number;
    palavrasPorSessao: number;
  };
  memorias: Record<string, unknown>;
  categorias: Record<string, unknown>;
}

/**
 * Função para buscar configurações do usuário
 * @param {string} username - Nome do usuário
 * @return {Promise<UserSettings | null>} Configurações do usuário
 */
async function getUserSettings(
  username: string
): Promise<UserSettings | null> {
  try {
    const settingsDoc = await admin.firestore()
      .collection("usuarios")
      .doc(username)
      .collection("configuracoes")
      .doc("settings")
      .get();

    if (!settingsDoc.exists) {
      console.error(
        `Configurações não encontradas para usuário: ${username}`
      );
      return null;
    }

    return settingsDoc.data() as UserSettings;
  } catch (error) {
    console.error("Erro ao buscar configurações do usuário:", error);
    return null;
  }
}

/**
 * Função para buscar configurações do chat
 * @param {string} username - Nome do usuário
 * @param {string} chatId - ID do chat
 * @return {Promise<ChatConfig | null>} Configurações do chat
 */
async function getChatConfig(
  username: string,
  chatId: string
): Promise<ChatConfig | null> {
  try {
    const chatDoc = await admin.firestore()
      .collection("usuarios")
      .doc(username)
      .collection("conversas")
      .doc(chatId)
      .get();

    if (!chatDoc.exists) {
      console.error(`Chat não encontrado: ${chatId}`);
      return null;
    }

    const data = chatDoc.data();
    return {
      systemPrompt: data?.systemPrompt || "",
      context: data?.context || "",
      temperature: data?.temperature || 1.0,
      frequencyPenalty: data?.frequencyPenalty || 1.0,
      repetitionPenalty: data?.repetitionPenalty || 1.0,
      maxTokens: data?.maxTokens || 2048,
      lastUsedModel: data?.lastUsedModel || "",
      latexInstructions: data?.latexInstructions || false,
    };
  } catch (error) {
    console.error("Erro ao buscar configurações do chat:", error);
    return null;
  }
}

/**
 * Função para carregar chat.json do Storage
 * @param {string} username - Nome do usuário
 * @param {string} chatId - ID do chat
 * @return {Promise<ChatData | null>} Dados do chat
 */
async function loadChatData(
  username: string,
  chatId: string
): Promise<ChatData | null> {
  try {
    const bucket = admin.storage().bucket();
    const filePath = `usuarios/${username}/conversas/${chatId}/chat.json`;
    const file = bucket.file(filePath);

    const [exists] = await file.exists();
    if (!exists) {
      console.error(`Arquivo chat.json não encontrado: ${filePath}`);
      return null;
    }

    const [contents] = await file.download();
    const chatData = JSON.parse(contents.toString()) as ChatData;

    return chatData;
  } catch (error) {
    console.error("Erro ao carregar chat.json:", error);
    return null;
  }
}

/**
 * Função para salvar chat.json atualizado
 * @param {string} username - Nome do usuário
 * @param {string} chatId - ID do chat
 * @param {ChatData} chatData - Dados do chat
 * @return {Promise<boolean>} Sucesso da operação
 */
async function saveChatData(
  username: string,
  chatId: string,
  chatData: ChatData
): Promise<boolean> {
  try {
    const bucket = admin.storage().bucket();
    const filePath = `usuarios/${username}/conversas/${chatId}/chat.json`;
    const file = bucket.file(filePath);

    // Atualizar timestamp
    chatData.lastUpdated = new Date().toISOString();

    // Salvar arquivo
    await file.save(JSON.stringify(chatData, null, 2), {
      metadata: {
        contentType: "application/json",
      },
    });

    return true;
  } catch (error) {
    console.error("Erro ao salvar chat.json:", error);
    return false;
  }
}



/**
 * Prompt LaTeX para instruir a IA
 */
const LATEX_INSTRUCTIONS_PROMPT = `# INSTRUÇÕES PARA USO DE LaTeX

Use LaTeX para expressões matemáticas:

## REGRAS BÁSICAS:
1. Use \`$...$\` para fórmulas inline: $x^2 + y^2 = z^2$
2. Use \`$$...$$\` para fórmulas em bloco:
$$\\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$

## SINTAXE COMUM:
- Frações: \`$\\frac{a}{b}$\`
- Potências: \`$x^2$\`, \`$x^{n+1}$\`
- Subscritos: \`$a_1$\`, \`$x_{i+1}$\`
- Raízes: \`$\\sqrt{x}$\`, \`$\\sqrt[3]{x}$\`
- Símbolos gregos: \`$\\pi$\`, \`$\\alpha$\`, \`$\\beta$\`

## EXEMPLOS:
- Correto: "A área do círculo é \`$A = \\pi r^2$\`"
- Correto: "Equação quadrática: \`$ax^2 + bx + c = 0$\`"

Use LaTeX sempre que houver matemática, mas mantenha o texto normal legível.`;

/**
 * Sistema de Chain of Thought (CoT)
 */
function buildCoTPrompt(
  messages: ChatMessage[],
  systemPrompt: string,
  context: string,
  latexInstructions: boolean = false
): unknown[] {
  const cotMessages: unknown[] = [];

  // Adicionar instruções LaTeX se habilitado
  if (latexInstructions) {
    cotMessages.push({
      role: "system",
      content: LATEX_INSTRUCTIONS_PROMPT,
    });
  }



  // Adicionar system prompt se existir
  if (systemPrompt.trim()) {
    cotMessages.push({
      role: "system",
      content: systemPrompt,
    });
  }

  // Adicionar contexto se existir
  if (context.trim()) {
    cotMessages.push({
      role: "system",
      content: `Contexto adicional: ${context}`,
    });
  }

  // Processar mensagens existentes para CoT
  if (messages.length > 0) {
    // Adicionar instrução de CoT para conversas existentes
    const cotInstruction = "Continue esta conversa de forma fluida e " +
      "concisa. Mantenha o contexto das mensagens anteriores e " +
      "responda de forma natural e útil.";

    cotMessages.push({
      role: "system",
      content: cotInstruction,
    });

    // Adicionar histórico de mensagens (limitado para evitar excesso)
    const recentMessages = messages.slice(-10); // Últimas 10 mensagens

    for (const message of recentMessages) {
      cotMessages.push({
        role: message.role,
        content: message.content,
      });
    }
  } else {
    // Para conversas novas, adicionar instrução inicial
    const initialInstruction = "Esta é uma nova conversa. Responda de " +
      "forma útil, clara e concisa. Seja amigável e profissional.";

    cotMessages.push({
      role: "system",
      content: initialInstruction,
    });
  }

  return cotMessages;
}

/**
 * Função para atualizar Firestore após nova mensagem
 * @param {string} username - Nome do usuário
 * @param {string} chatId - ID do chat
 * @param {string} lastMessage - Última mensagem
 * @return {Promise<boolean>} Sucesso da operação
 */
async function updateChatInFirestore(
  username: string,
  chatId: string,
  lastMessage: string
): Promise<boolean> {
  try {
    const now = new Date().toISOString();

    await admin.firestore()
      .collection("usuarios")
      .doc(username)
      .collection("conversas")
      .doc(chatId)
      .update({
        ultimaMensagem: lastMessage,
        ultimaMensagemEm: now,
        lastUpdatedAt: now,
        updatedAt: now,
      });

    return true;
  } catch (error) {
    console.error("Erro ao atualizar chat no Firestore:", error);
    return false;
  }
}

/**
 * Função para gerar ID único para mensagens
 * @return {string} ID único
 */
function generateMessageId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

/**
 * Função para validar entrada da requisição
 * @param {unknown} body - Corpo da requisição
 * @return {Object} Resultado da validação
 */
function validateRequest(body: unknown): {isValid: boolean; error?: string} {
  if (!body) {
    return {isValid: false, error: "Corpo da requisição é obrigatório"};
  }

  const {username, chatId, message} = body as Record<string, unknown>;

  if (!username || typeof username !== "string" ||
      username.trim().length === 0) {
    return {
      isValid: false,
      error: "Username é obrigatório e deve ser uma string não vazia",
    };
  }

  if (!chatId || typeof chatId !== "string" || chatId.trim().length === 0) {
    return {
      isValid: false,
      error: "ChatId é obrigatório e deve ser uma string não vazia",
    };
  }

  if (!message || typeof message !== "string" ||
      message.trim().length === 0) {
    return {
      isValid: false,
      error: "Message é obrigatória e deve ser uma string não vazia",
    };
  }

  if (message.length > 10000) {
    return {
      isValid: false,
      error: "Mensagem muito longa (máximo 10.000 caracteres)",
    };
  }

  return {isValid: true};
}

/**
 * Função para log estruturado
 * @param {string} message - Mensagem de log
 * @param {unknown} data - Dados adicionais
 */
function logInfo(message: string, data?: unknown) {
  functions.logger.info(message, data);
  console.log(`[INFO] ${message}`, data ? JSON.stringify(data, null, 2) : "");
}

/**
 * Função para log de erro
 * @param {string} message - Mensagem de erro
 * @param {unknown} error - Erro
 */
function logError(message: string, error?: unknown) {
  console.error(`[ERROR] ${message}`, error);
}

/**
 * Função para log de warning
 * @param {string} message - Mensagem de warning
 * @param {unknown} data - Dados adicionais
 */
function logWarning(message: string, data?: unknown) {
  console.warn(`[WARNING] ${message}`,
    data ? JSON.stringify(data, null, 2) : "");
}

// Função principal para streaming com OpenRouter
export const chatWithAI = functions.https.onRequest(async (req, res) => {
  // Configurar CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type, Authorization");

  if (req.method === "OPTIONS") {
    res.status(200).send("");
    return;
  }

  if (req.method !== "POST") {
    res.status(405).json({error: "Método não permitido"});
    return;
  }

  try {
    logInfo("=== DEBUG: NOVA REQUISIÇÃO RECEBIDA ===");
    logInfo("Method: " + req.method);
    logInfo("Headers", req.headers);
    logInfo("Body completo", req.body);
    logInfo("Body type: " + typeof req.body);

    // Validar entrada
    const validation = validateRequest(req.body);
    if (!validation.isValid) {
      console.log("=== DEBUG: VALIDAÇÃO FALHOU ===");
      console.log("Erro de validação:", validation.error);
      logWarning("Requisição inválida", {
        error: validation.error,
        body: req.body,
      });
      res.status(400).json({error: validation.error});
      return;
    }

    const {username, chatId, message, model} = req.body;
    logInfo("=== DEBUG: DADOS EXTRAÍDOS DA REQUISIÇÃO ===");
    logInfo("Username: " + username + " (tipo: " + typeof username + ")");
    logInfo("ChatId: " + chatId + " (tipo: " + typeof chatId + ")");
    logInfo("Message length: " + message?.length + " (tipo: " + typeof message + ")");
    logInfo("Model: " + model + " (tipo: " + typeof model + ")");
    logInfo("Message preview: " + (message?.substring(0, 100) + (message?.length > 100 ? "..." : "")));

    logInfo("Processando requisição", {
      username,
      chatId,
      messageLength: message.length,
      model,
    });

    // Buscar configurações do usuário
    const userSettings = await getUserSettings(username);
    if (!userSettings) {
      logError("Configurações do usuário não encontradas", {username});
      res.status(404).json({error: "Configurações do usuário não encontradas"});
      return;
    }

    // Buscar configurações do chat
    const chatConfig = await getChatConfig(username, chatId);
    if (!chatConfig) {
      logError("Chat não encontrado", {username, chatId});
      res.status(404).json({error: "Chat não encontrado"});
      return;
    }

    // Carregar dados do chat
    const chatData = await loadChatData(username, chatId);
    if (!chatData) {
      logError("Dados do chat não encontrados", {username, chatId});
      res.status(404).json({error: "Dados do chat não encontrados"});
      return;
    }

    // Encontrar endpoint ativo
    const activeEndpoint = Object.values(userSettings.endpoints)
      .find((endpoint) => endpoint.ativo);
    if (!activeEndpoint || !activeEndpoint.apiKey) {
      logError("Endpoint ativo não encontrado ou sem API key", {
        username,
        hasEndpoints: Object.keys(userSettings.endpoints).length > 0,
        activeEndpoints: Object.values(userSettings.endpoints)
          .filter((e) => e.ativo).length,
      });
      const errorMsg = "Nenhum endpoint ativo encontrado ou " +
        "API key não configurada";
      res.status(400).json({error: errorMsg});
      return;
    }

    // Usar modelo especificado ou padrão do endpoint
    const selectedModel = model || chatConfig.lastUsedModel ||
      activeEndpoint.modeloPadrao;

    // Adicionar mensagem do usuário ao chat
    const userMessage: ChatMessage = {
      id: generateMessageId(),
      content: message,
      role: "user",
      timestamp: new Date().toISOString(),
    };

    chatData.messages.push(userMessage);

    // Construir prompt com CoT
    const cotMessages = buildCoTPrompt(
      chatData.messages,
      chatConfig.systemPrompt,
      chatConfig.context,
      chatConfig.latexInstructions
    );

    // Preparar payload para OpenRouter
    const payload = {
      model: selectedModel,
      messages: cotMessages,
      stream: true,
      temperature: chatConfig.temperature,
      frequency_penalty: chatConfig.frequencyPenalty,
      presence_penalty: chatConfig.repetitionPenalty,
      max_tokens: chatConfig.maxTokens,
    };

    // DEBUG: Log detalhado de todos os parâmetros
    logInfo("=== DEBUG: PARÂMETROS COMPLETOS DA REQUISIÇÃO ===");
    logInfo("1. ENDPOINT ATIVO", {
      nome: activeEndpoint.nome,
      url: activeEndpoint.url,
      apiKeyPreview: activeEndpoint.apiKey?.substring(0, 10) + "...",
      modeloPadrao: activeEndpoint.modeloPadrao,
      ativo: activeEndpoint.ativo
    });

    logInfo("2. MODELO SELECIONADO", {
      modeloRequest: model,
      ultimoModeloChat: chatConfig.lastUsedModel,
      modeloPadraoEndpoint: activeEndpoint.modeloPadrao,
      MODELO_FINAL_SELECIONADO: selectedModel
    });

    logInfo("3. CONFIGURAÇÕES DO CHAT", {
      temperature: chatConfig.temperature,
      temperatureType: typeof chatConfig.temperature,
      frequencyPenalty: chatConfig.frequencyPenalty,
      frequencyPenaltyType: typeof chatConfig.frequencyPenalty,
      repetitionPenalty: chatConfig.repetitionPenalty,
      repetitionPenaltyType: typeof chatConfig.repetitionPenalty,
      maxTokens: chatConfig.maxTokens,
      maxTokensType: typeof chatConfig.maxTokens,
      systemPromptLength: chatConfig.systemPrompt?.length || 0,
      contextLength: chatConfig.context?.length || 0,
      latexInstructions: chatConfig.latexInstructions
    });

    console.log("4. MENSAGENS CoT:");
    console.log("   - Total de mensagens:", cotMessages.length);
    cotMessages.forEach((msg, index) => {
      const msgObj = msg as {role: string; content: string};
      console.log(`   - Mensagem ${index + 1}:`, {
        role: msgObj.role,
        contentLength: msgObj.content?.length || 0,
        contentPreview: msgObj.content?.substring(0, 100) + (msgObj.content?.length > 100 ? "..." : "")
      });
    });

    console.log("5. PAYLOAD FINAL:");
    console.log("   - Model:", payload.model);
    console.log("   - Stream:", payload.stream);
    console.log("   - Temperature:", payload.temperature, "(tipo:", typeof payload.temperature, ")");
    console.log("   - Frequency Penalty:", payload.frequency_penalty, "(tipo:", typeof payload.frequency_penalty, ")");
    console.log("   - Presence Penalty:", payload.presence_penalty, "(tipo:", typeof payload.presence_penalty, ")");
    console.log("   - Max Tokens:", payload.max_tokens, "(tipo:", typeof payload.max_tokens, ")");
    console.log("   - Messages count:", payload.messages.length);

    console.log("6. HEADERS DA REQUISIÇÃO:");
    const headers = {
      "Authorization": `Bearer ${activeEndpoint.apiKey}`,
      "Content-Type": "application/json",
    };
    console.log("   - Authorization: Bearer", activeEndpoint.apiKey?.substring(0, 10) + "...");
    console.log("   - Content-Type:", headers["Content-Type"]);

    console.log("7. PAYLOAD JSON (primeiros 500 chars):");
    const payloadJson = JSON.stringify(payload);
    console.log("   - JSON length:", payloadJson.length);
    console.log("   - JSON preview:", payloadJson.substring(0, 500) + (payloadJson.length > 500 ? "..." : ""));

    console.log("=== FIM DEBUG PARÂMETROS ===");

    logInfo("Fazendo requisição para API", {
      url: activeEndpoint.url,
      model: selectedModel,
      messagesCount: cotMessages.length,
      temperature: chatConfig.temperature,
      maxTokens: chatConfig.maxTokens,
    });

    // Fazer requisição para OpenRouter
    console.log("=== DEBUG: FAZENDO REQUISIÇÃO ===");
    console.log("URL:", activeEndpoint.url);
    console.log("Method: POST");
    console.log("Enviando requisição...");

    const response = await fetch(activeEndpoint.url, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${activeEndpoint.apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    console.log("=== DEBUG: RESPOSTA RECEBIDA ===");
    console.log("Status:", response.status);
    console.log("Status Text:", response.statusText);
    console.log("Headers:", Object.fromEntries(response.headers.entries()));
    console.log("OK:", response.ok);

    if (!response.ok) {
      const errorText = await response.text();
      console.log("=== DEBUG: ERRO NA API ===");
      console.log("Error Text:", errorText);
      console.log("Tentando fazer parse do erro como JSON...");

      try {
        const errorJson = JSON.parse(errorText);
        console.log("Erro JSON parseado:", JSON.stringify(errorJson, null, 2));
      } catch (e) {
        console.log("Erro não é JSON válido, texto puro:", errorText);
      }

      logError("Erro na API externa", {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
        url: activeEndpoint.url,
        model: selectedModel,
      });
      res.status(response.status).json({error: `Erro na API: ${errorText}`});
      return;
    }

    // Configurar streaming
    res.setHeader("Content-Type", "text/plain; charset=utf-8");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");

    let assistantMessage = "";
    let buffer = "";
    let chunkCount = 0;
    let totalDataEvents = 0;
    let contentChunks = 0;

    console.log("=== DEBUG: INICIANDO PROCESSAMENTO DO STREAM ===");

    // Processar stream
    if (response.body) {
      // Usar ReadableStream do Node.js
      const stream = response.body as NodeJS.ReadableStream;

      try {
        for await (const chunk of stream) {
          chunkCount++;
          const chunkStr = chunk.toString();
          buffer += chunkStr;

          if (chunkCount <= 5) {
            console.log(`Chunk ${chunkCount} recebido (${chunkStr.length} chars):`,
              chunkStr.substring(0, 200) + (chunkStr.length > 200 ? "..." : ""));
          }

          // Processar linhas completas do buffer
          let lineEnd = buffer.indexOf("\n");
          while (lineEnd !== -1) {
            const line = buffer.slice(0, lineEnd).trim();
            buffer = buffer.slice(lineEnd + 1);

            if (line.startsWith("data: ")) {
              totalDataEvents++;
              const data = line.slice(6);

              if (totalDataEvents <= 10) {
                console.log(`Data event ${totalDataEvents}:`, data.substring(0, 200) + (data.length > 200 ? "..." : ""));
              }

              if (data === "[DONE]") {
                console.log("Stream finalizado com [DONE]");
                break;
              }

              // Ignorar comentários SSE
              if (data.startsWith(":")) {
                console.log("Comentário SSE ignorado:", data);
                lineEnd = buffer.indexOf("\n");
                continue;
              }

              try {
                const parsed = JSON.parse(data);

                if (totalDataEvents <= 5) {
                  console.log(`Parsed JSON ${totalDataEvents}:`, JSON.stringify(parsed, null, 2));
                }

                const content = parsed.choices?.[0]?.delta?.content;
                if (content) {
                  contentChunks++;
                  assistantMessage += content;
                  res.write(content);

                  if (contentChunks <= 5) {
                    console.log(`Content chunk ${contentChunks}:`, JSON.stringify(content));
                  }
                } else if (parsed.choices?.[0]?.delta) {
                  // Log outros tipos de delta que não são content
                  const delta = parsed.choices[0].delta;
                  if (totalDataEvents <= 10) {
                    console.log(`Delta sem content ${totalDataEvents}:`, JSON.stringify(delta));
                  }
                }

                // Log se há erros na resposta
                if (parsed.error) {
                  console.log("ERRO na resposta da API:", JSON.stringify(parsed.error, null, 2));
                }

              } catch (e) {
                // Ignorar JSON inválido
                console.log("JSON inválido no stream:", {
                  data: data.substring(0, 200),
                  error: e instanceof Error ? e.message : String(e)
                });
                logWarning("JSON inválido ignorado no stream",
                  {data, error: e});
              }
            } else if (line.trim() && !line.startsWith(":")) {
              console.log("Linha não-data ignorada:", line);
            }
            lineEnd = buffer.indexOf("\n");
          }
        }

        console.log("=== DEBUG: ESTATÍSTICAS DO STREAM ===");
        console.log("Total de chunks recebidos:", chunkCount);
        console.log("Total de data events:", totalDataEvents);
        console.log("Total de content chunks:", contentChunks);
        console.log("Tamanho da mensagem final:", assistantMessage.length);
        console.log("Primeiros 200 chars da resposta:", assistantMessage.substring(0, 200));

      } catch (streamError) {
        console.log("=== DEBUG: ERRO NO STREAM ===");
        console.log("Stream error:", streamError);
        console.log("Buffer atual:", buffer.substring(0, 500));
        console.log("Mensagem parcial:", assistantMessage.substring(0, 200));

        logError("Erro no processamento do stream", streamError);
        if (!res.headersSent) {
          res.status(500).json({error: "Erro no processamento do stream"});
          return;
        }
      }
    } else {
      console.log("=== DEBUG: SEM BODY NA RESPOSTA ===");
      console.log("Response body é null/undefined");
    }

    // Finalizar stream
    res.end();

    console.log("=== DEBUG: FINALIZANDO PROCESSAMENTO ===");
    console.log("Mensagem da IA (length):", assistantMessage.length);
    console.log("Mensagem da IA (trimmed length):", assistantMessage.trim().length);
    console.log("Mensagem da IA (primeiros 300 chars):", assistantMessage.substring(0, 300));

    // Salvar resposta da IA no chat
    if (assistantMessage.trim()) {
      console.log("Salvando mensagem da IA...");

      const aiMessage: ChatMessage = {
        id: generateMessageId(),
        content: assistantMessage.trim(),
        role: "assistant",
        timestamp: new Date().toISOString(),
      };

      console.log("Mensagem da IA criada:", {
        id: aiMessage.id,
        contentLength: aiMessage.content.length,
        role: aiMessage.role,
        timestamp: aiMessage.timestamp
      });

      chatData.messages.push(aiMessage);
      console.log("Total de mensagens no chat após adicionar IA:", chatData.messages.length);

      // Salvar chat atualizado
      console.log("Salvando chat.json...");
      const chatSaved = await saveChatData(username, chatId, chatData);
      console.log("Chat.json salvo:", chatSaved);
      if (!chatSaved) {
        logError("Falha ao salvar chat.json", {username, chatId});
      }

      // Atualizar Firestore
      console.log("Atualizando Firestore...");
      const firestoreUpdated = await updateChatInFirestore(
        username,
        chatId,
        assistantMessage.trim()
      );
      console.log("Firestore atualizado:", firestoreUpdated);
      if (!firestoreUpdated) {
        logError("Falha ao atualizar Firestore", {username, chatId});
      }

      console.log("=== DEBUG: PROCESSAMENTO CONCLUÍDO COM SUCESSO ===");
      logInfo("Conversa processada com sucesso", {
        username,
        chatId,
        messageLength: assistantMessage.length,
        chatSaved,
        firestoreUpdated,
      });
    } else {
      console.log("=== DEBUG: RESPOSTA DA IA VAZIA ===");
      console.log("assistantMessage:", JSON.stringify(assistantMessage));
      console.log("assistantMessage.trim():", JSON.stringify(assistantMessage.trim()));
      logWarning("Resposta da IA vazia", {username, chatId});
    }
  } catch (error) {
    logError("Erro no processamento geral", error);
    if (!res.headersSent) {
      res.status(500).json({error: "Erro interno do servidor"});
    }
  }
});
