"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n// Serviço para integração com Firebase Functions de IA\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            var _requestData_message, _requestData_message1, _requestData_message2;\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model\n            };\n            // DEBUG: Log detalhado no console do navegador\n            console.group(\"\\uD83E\\uDD16 AI SERVICE - ENVIANDO REQUISI\\xc7\\xc3O\");\n            console.log(\"\\uD83D\\uDCE1 URL da Function:\", this.functionUrl);\n            console.log(\"\\uD83D\\uDCCB Dados da Requisi\\xe7\\xe3o:\", {\n                username: requestData.username,\n                chatId: requestData.chatId,\n                message: requestData.message,\n                messageLength: ((_requestData_message = requestData.message) === null || _requestData_message === void 0 ? void 0 : _requestData_message.length) || 0,\n                messagePreview: ((_requestData_message1 = requestData.message) === null || _requestData_message1 === void 0 ? void 0 : _requestData_message1.substring(0, 100)) + (((_requestData_message2 = requestData.message) === null || _requestData_message2 === void 0 ? void 0 : _requestData_message2.length) > 100 ? \"...\" : \"\"),\n                model: requestData.model,\n                timestamp: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDD27 Headers:\", {\n                \"Content-Type\": \"application/json\",\n                \"Method\": \"POST\"\n            });\n            console.log(\"\\uD83D\\uDCE6 Body JSON:\", JSON.stringify(requestData, null, 2));\n            console.log(\"\\uD83D\\uDCCA Estat\\xedsticas:\", {\n                bodySize: JSON.stringify(requestData).length + \" bytes\",\n                hasAbortController: !!this.abortController,\n                userAgent: navigator.userAgent\n            });\n            // Buscar configurações do chat do Firestore para mostrar nos logs\n            console.log(\"\\uD83D\\uDD0D Buscando configura\\xe7\\xf5es do chat do Firestore para debug...\");\n            try {\n                // Importar Firebase dinamicamente para evitar problemas de SSR\n                const { doc, getDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                const { db } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\"));\n                const chatDocRef = doc(db, \"usuarios\", requestData.username, \"conversas\", requestData.chatId);\n                const chatDocSnap = await getDoc(chatDocRef);\n                if (chatDocSnap.exists()) {\n                    var _chatConfig_systemPrompt, _chatConfig_systemPrompt1, _chatConfig_systemPrompt2, _chatConfig_context, _chatConfig_context1, _chatConfig_context2;\n                    const chatConfig = chatDocSnap.data();\n                    console.group(\"⚙️ CONFIGURA\\xc7\\xd5ES DO CHAT (FIRESTORE)\");\n                    console.log(\"\\uD83C\\uDFAF System Prompt:\", {\n                        length: ((_chatConfig_systemPrompt = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt === void 0 ? void 0 : _chatConfig_systemPrompt.length) || 0,\n                        content: chatConfig.systemPrompt || \"(vazio)\",\n                        preview: ((_chatConfig_systemPrompt1 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt1 === void 0 ? void 0 : _chatConfig_systemPrompt1.substring(0, 200)) + (((_chatConfig_systemPrompt2 = chatConfig.systemPrompt) === null || _chatConfig_systemPrompt2 === void 0 ? void 0 : _chatConfig_systemPrompt2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83D\\uDCDD Contexto:\", {\n                        length: ((_chatConfig_context = chatConfig.context) === null || _chatConfig_context === void 0 ? void 0 : _chatConfig_context.length) || 0,\n                        content: chatConfig.context || \"(vazio)\",\n                        preview: ((_chatConfig_context1 = chatConfig.context) === null || _chatConfig_context1 === void 0 ? void 0 : _chatConfig_context1.substring(0, 200)) + (((_chatConfig_context2 = chatConfig.context) === null || _chatConfig_context2 === void 0 ? void 0 : _chatConfig_context2.length) > 200 ? \"...\" : \"\")\n                    });\n                    console.log(\"\\uD83C\\uDF21️ Par\\xe2metros de Gera\\xe7\\xe3o:\", {\n                        temperature: chatConfig.temperature,\n                        temperatureType: typeof chatConfig.temperature,\n                        frequencyPenalty: chatConfig.frequencyPenalty,\n                        frequencyPenaltyType: typeof chatConfig.frequencyPenalty,\n                        repetitionPenalty: chatConfig.repetitionPenalty,\n                        repetitionPenaltyType: typeof chatConfig.repetitionPenalty,\n                        maxTokens: chatConfig.maxTokens,\n                        maxTokensType: typeof chatConfig.maxTokens,\n                        latexInstructions: chatConfig.latexInstructions\n                    });\n                    console.log(\"\\uD83D\\uDCCA Metadados:\", {\n                        lastUsedModel: chatConfig.lastUsedModel,\n                        createdAt: chatConfig.createdAt,\n                        lastUpdatedAt: chatConfig.lastUpdatedAt,\n                        name: chatConfig.name\n                    });\n                    console.groupEnd();\n                } else {\n                    console.warn(\"⚠️ Documento do chat n\\xe3o encontrado no Firestore\");\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar configura\\xe7\\xf5es do chat do Firestore:\", error);\n            }\n            // Buscar histórico de mensagens do Storage para mostrar nos logs\n            console.log(\"\\uD83D\\uDD0D Buscando hist\\xf3rico de mensagens do Storage para debug...\");\n            try {\n                const messagesResponse = await fetch(\"/api/chat/\".concat(requestData.username, \"/\").concat(requestData.chatId));\n                if (messagesResponse.ok) {\n                    const chatData = await messagesResponse.json();\n                    const messages = chatData.messages || [];\n                    console.group(\"\\uD83D\\uDCAC HIST\\xd3RICO DE MENSAGENS (STORAGE)\");\n                    console.log(\"\\uD83D\\uDCCA Total de mensagens:\", messages.length);\n                    if (messages.length > 0) {\n                        var _messages_, _messages_1;\n                        console.log(\"\\uD83D\\uDCCB \\xdaltimas 5 mensagens:\", messages.slice(-5).map((msg, index)=>{\n                            var _msg_content, _msg_content1, _msg_content2, _msg_attachments;\n                            return {\n                                index: messages.length - 5 + index,\n                                id: msg.id,\n                                role: msg.role,\n                                contentLength: ((_msg_content = msg.content) === null || _msg_content === void 0 ? void 0 : _msg_content.length) || 0,\n                                contentPreview: ((_msg_content1 = msg.content) === null || _msg_content1 === void 0 ? void 0 : _msg_content1.substring(0, 100)) + (((_msg_content2 = msg.content) === null || _msg_content2 === void 0 ? void 0 : _msg_content2.length) > 100 ? \"...\" : \"\"),\n                                timestamp: msg.timestamp,\n                                hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                                attachmentCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0\n                            };\n                        }));\n                        if (messages.length > 5) {\n                            console.log(\"\\uD83D\\uDCDC Mensagens mais antigas:\", \"\".concat(messages.length - 5, \" mensagens anteriores n\\xe3o mostradas\"));\n                        }\n                        // Estatísticas do histórico\n                        const userMessages = messages.filter((msg)=>msg.role === \"user\").length;\n                        const assistantMessages = messages.filter((msg)=>msg.role === \"assistant\").length;\n                        const totalChars = messages.reduce((acc, msg)=>{\n                            var _msg_content;\n                            return acc + (((_msg_content = msg.content) === null || _msg_content === void 0 ? void 0 : _msg_content.length) || 0);\n                        }, 0);\n                        console.log(\"\\uD83D\\uDCC8 Estat\\xedsticas do Hist\\xf3rico:\", {\n                            totalMessages: messages.length,\n                            userMessages,\n                            assistantMessages,\n                            totalCharacters: totalChars,\n                            averageMessageLength: messages.length > 0 ? Math.round(totalChars / messages.length) : 0,\n                            oldestMessage: (_messages_ = messages[0]) === null || _messages_ === void 0 ? void 0 : _messages_.timestamp,\n                            newestMessage: (_messages_1 = messages[messages.length - 1]) === null || _messages_1 === void 0 ? void 0 : _messages_1.timestamp\n                        });\n                    } else {\n                        console.log(\"\\uD83D\\uDCED Nenhuma mensagem encontrada no hist\\xf3rico\");\n                    }\n                    console.groupEnd();\n                } else {\n                    console.warn(\"⚠️ Falha ao buscar mensagens - Status:\", messagesResponse.status);\n                }\n            } catch (error) {\n                console.warn(\"⚠️ N\\xe3o foi poss\\xedvel buscar hist\\xf3rico de mensagens:\", error);\n            }\n            console.groupEnd();\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            // DEBUG: Log da resposta\n            console.group(\"\\uD83D\\uDCE1 AI SERVICE - RESPOSTA RECEBIDA\");\n            console.log(\"✅ Status:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCCB Headers:\", Object.fromEntries(response.headers.entries()));\n            console.log(\"\\uD83D\\uDD0D Response OK:\", response.ok);\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            // Processar stream\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    return;\n                }\n                onError(error.message);\n            } else {\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        var _config_message;\n        console.group(\"\\uD83D\\uDE80 AI SERVICE - SEND MESSAGE SAFE\");\n        console.log(\"\\uD83D\\uDD27 Config recebida:\", {\n            username: config.username,\n            chatId: config.chatId,\n            messageLength: ((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.length) || 0,\n            model: config.model,\n            timestamp: new Date().toISOString()\n        });\n        console.log(\"\\uD83D\\uDCCB Callbacks:\", {\n            hasOnChunk: typeof onChunk === \"function\",\n            hasOnComplete: typeof onComplete === \"function\",\n            hasOnError: typeof onError === \"function\"\n        });\n        try {\n            console.log(\"✅ Validando configura\\xe7\\xe3o...\");\n            this.validateConfig(config);\n            console.log(\"✅ Configura\\xe7\\xe3o v\\xe1lida, enviando mensagem...\");\n            await this.sendMessage(config, onChunk, onComplete, onError);\n            console.log(\"✅ Mensagem enviada com sucesso\");\n            console.groupEnd();\n        } catch (error) {\n            console.group(\"❌ ERRO EM SEND MESSAGE SAFE\");\n            console.error(\"\\uD83D\\uDEA8 Erro capturado:\", error);\n            console.error(\"\\uD83D\\uDCDD Tipo:\", error instanceof Error ? error.constructor.name : typeof error);\n            console.groupEnd();\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n            console.groupEnd();\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});